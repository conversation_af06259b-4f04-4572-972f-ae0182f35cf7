/**
 * @file auth_web_server.hpp
 * @brief Augment Authentication Web Server - C++17实现
 * <AUTHOR> Agent
 * @date 2025-01-05
 * 
 * 现代C++实现的Augment认证Web服务器
 * 提供OAuth认证流程的Web界面和API端点
 * 
 * 编译要求:
 * - C++17或更高版本
 * - httplib库 (HTTP服务器)
 * - nlohmann/json库 (JSON处理)
 * - OpenSSL库 (加密功能)
 * 
 * 编译示例:
 * g++ -std=c++17 -Wall -Wextra -O2 -I/path/to/httplib -I/path/to/nlohmann/json \
 *     auth_web_server.cpp main.cpp -lssl -lcrypto -lpthread -o auth_server
 */

#pragma once

#include <string>
#include <string_view>
#include <memory>
#include <unordered_map>
#include <functional>
#include <future>
#include <chrono>
#include <optional>
#include <vector>
#include <mutex>
#include <atomic>

// 第三方库依赖
#ifdef HAVE_NLOHMANN_JSON
#include <nlohmann/json.hpp>
#else
#include "simple_json.hpp"
#endif
#include <httplib.h>

// 前向声明
namespace augment {
    class AugmentAuthManager;
}

namespace augment {

/**
 * @brief HTTP请求处理异常类
 */
class HttpException : public std::runtime_error {
public:
    explicit HttpException(int status_code, const std::string& message)
        : std::runtime_error(message), status_code_(status_code) {}
    
    [[nodiscard]] int status_code() const noexcept { return status_code_; }

private:
    int status_code_;
};

/**
 * @brief 服务器配置结构
 */
struct ServerConfig {
    std::string host = "localhost";
    int port = 3000;
    bool enable_cors = true;
    std::chrono::seconds request_timeout{30};
    std::size_t max_request_size = 1024 * 1024; // 1MB
    bool debug_mode = false;
    
    // 认证管理器配置
    struct AuthManagerConfig {
        bool debug = false;
        std::string client_secret;
        std::string session_store_path = "./sessions";
    } auth_manager;
};

/**
 * @brief 认证Web服务器类
 * 
 * 提供OAuth认证流程的Web界面和RESTful API
 * 支持多账户管理、token刷新、会话存储等功能
 */
class AuthWebServer {
public:
    /**
     * @brief 构造函数
     * @param config 服务器配置
     */
    explicit AuthWebServer(const ServerConfig& config = {});
    
    /**
     * @brief 析构函数
     */
    ~AuthWebServer();
    
    // 禁用拷贝，允许移动
    AuthWebServer(const AuthWebServer&) = delete;
    AuthWebServer& operator=(const AuthWebServer&) = delete;
    AuthWebServer(AuthWebServer&&) = default;
    AuthWebServer& operator=(AuthWebServer&&) = default;
    
    /**
     * @brief 启动Web服务器
     * @return 启动成功的Future
     * @throws std::runtime_error 启动失败时
     */
    [[nodiscard]] std::future<void> start();
    
    /**
     * @brief 停止Web服务器
     * @return 停止完成的Future
     */
    [[nodiscard]] std::future<void> stop();
    
    /**
     * @brief 检查服务器是否正在运行
     * @return 如果服务器正在运行则返回true
     */
    [[nodiscard]] bool is_running() const noexcept;
    
    /**
     * @brief 获取服务器URL
     * @return 服务器完整URL
     */
    [[nodiscard]] std::string get_server_url() const;
    
    /**
     * @brief 设置自定义路由处理器
     * @param path 路径
     * @param handler 处理函数
     */
    void set_custom_handler(const std::string& path, 
                           std::function<void(const httplib::Request&, httplib::Response&)> handler);

private:
    /**
     * @brief 初始化HTTP服务器路由
     */
    void setup_routes();
    
    /**
     * @brief 设置CORS头部
     * @param res HTTP响应对象
     */
    void set_cors_headers(httplib::Response& res) const;
    
    /**
     * @brief 发送JSON响应
     * @param res HTTP响应对象
     * @param json_data JSON数据
     * @param status_code HTTP状态码
     */
    void send_json_response(httplib::Response& res, 
                           const nlohmann::json& json_data, 
                           int status_code = 200) const;
    
    /**
     * @brief 发送错误响应
     * @param res HTTP响应对象
     * @param status_code HTTP状态码
     * @param message 错误消息
     */
    void send_error_response(httplib::Response& res, 
                            int status_code, 
                            const std::string& message) const;
    
    /**
     * @brief 解析请求体JSON
     * @param req HTTP请求对象
     * @return 解析后的JSON对象
     * @throws HttpException JSON解析失败时
     */
    [[nodiscard]] nlohmann::json parse_request_json(const httplib::Request& req) const;

    /**
     * @brief 获取查询参数
     * @param req HTTP请求对象
     * @param key 参数名
     * @return 参数值，如果不存在则返回空字符串
     */
    [[nodiscard]] std::string get_query_param(const httplib::Request& req, const std::string& key) const;
    
    // ========== 路由处理器 ==========
    
    /**
     * @brief 处理根路径请求（Web界面）
     */
    void handle_web_interface(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 处理开始认证请求
     */
    void handle_start_auth(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 处理完成认证请求
     */
    void handle_complete_auth(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 处理账户列表请求
     */
    void handle_accounts(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 处理当前账户请求
     */
    void handle_current_account(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 处理账户切换请求
     */
    void handle_switch_account(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 处理账户移除请求
     */
    void handle_remove_account(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 处理token刷新请求
     */
    void handle_refresh_token(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 处理OAuth回调请求
     */
    void handle_oauth_callback(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 处理OPTIONS请求（CORS预检）
     */
    void handle_options(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 生成Web界面HTML
     * @return HTML字符串
     */
    [[nodiscard]] std::string generate_web_interface_html() const;
    
    /**
     * @brief 异常处理包装器
     * @param handler 原始处理函数
     * @return 包装后的处理函数
     */
    std::function<void(const httplib::Request&, httplib::Response&)>
    wrap_handler(std::function<void(const httplib::Request&, httplib::Response&)> handler);

private:
    ServerConfig config_;
    std::unique_ptr<httplib::Server> server_;
    std::unique_ptr<AugmentAuthManager> auth_manager_;
    std::atomic<bool> running_{false};
    mutable std::mutex server_mutex_;
    
    // 待处理的认证流程
    std::unordered_map<std::string, nlohmann::json> pending_flows_;
    mutable std::mutex pending_flows_mutex_;
};

/**
 * @brief 服务器构建器类
 * 
 * 提供流式API来配置和创建AuthWebServer实例
 */
class AuthWebServerBuilder {
public:
    AuthWebServerBuilder() = default;
    
    AuthWebServerBuilder& host(const std::string& host) {
        config_.host = host;
        return *this;
    }
    
    AuthWebServerBuilder& port(int port) {
        config_.port = port;
        return *this;
    }
    
    AuthWebServerBuilder& enable_cors(bool enable = true) {
        config_.enable_cors = enable;
        return *this;
    }
    
    AuthWebServerBuilder& debug_mode(bool debug = true) {
        config_.debug_mode = debug;
        config_.auth_manager.debug = debug;
        return *this;
    }
    
    AuthWebServerBuilder& client_secret(const std::string& secret) {
        config_.auth_manager.client_secret = secret;
        return *this;
    }
    
    AuthWebServerBuilder& session_store_path(const std::string& path) {
        config_.auth_manager.session_store_path = path;
        return *this;
    }
    
    AuthWebServerBuilder& request_timeout(std::chrono::seconds timeout) {
        config_.request_timeout = timeout;
        return *this;
    }
    
    AuthWebServerBuilder& max_request_size(std::size_t size) {
        config_.max_request_size = size;
        return *this;
    }
    
    [[nodiscard]] std::unique_ptr<AuthWebServer> build() {
        return std::make_unique<AuthWebServer>(config_);
    }

private:
    ServerConfig config_;
};

} // namespace augment
