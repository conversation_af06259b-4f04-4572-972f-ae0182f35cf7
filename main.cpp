/**
 * @file main.cpp
 * @brief Augment认证Web服务器主程序
 */

#include "auth_web_server.hpp"
#include <iostream>
#include <csignal>
#include <memory>
#include <thread>
#include <chrono>
#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

using namespace augment;

// 全局服务器实例（用于信号处理）
std::unique_ptr<AuthWebServer> g_server;

/**
 * @brief 设置Windows控制台编码以支持中文显示
 * 修复中文乱码问题
 */
void setup_console_encoding() {
#ifdef _WIN32
    // 设置控制台代码页为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // 设置控制台模式以支持虚拟终端序列（用于颜色输出）
    HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
    if (hOut != INVALID_HANDLE_VALUE) {
        DWORD dwMode = 0;
        if (GetConsoleMode(hOut, &dwMode)) {
            dwMode |= ENABLE_VIRTUAL_TERMINAL_PROCESSING;
            SetConsoleMode(hOut, dwMode);
        }
    }

    // 设置C++流的locale为UTF-8
    std::locale::global(std::locale(""));
    std::cout.imbue(std::locale());
    std::cerr.imbue(std::locale());
#endif
}

/**
 * @brief 信号处理函数
 */
void signal_handler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down server...\n";

    if (g_server) {
        try {
            auto stop_future = g_server->stop();
            stop_future.wait();
            std::cout << "Server shutdown safely\n";
        } catch (const std::exception& e) {
            std::cerr << "Error shutting down server: " << e.what() << "\n";
        }
    }

    std::exit(0);
}

/**
 * @brief 打印使用说明
 */
void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [options]\n\n";
    std::cout << "Options:\n";
    std::cout << "  -h, --help              Show this help message\n";
    std::cout << "  -p, --port PORT         Set server port (default: 3000)\n";
    std::cout << "  -H, --host HOST         Set server host (default: localhost)\n";
    std::cout << "  -d, --debug             Enable debug mode\n";
    std::cout << "  --no-cors               Disable CORS support\n";
    std::cout << "  --session-path PATH     Set session storage path (default: ./sessions)\n";
    std::cout << "  --client-secret SECRET  Set OAuth client secret\n";
    std::cout << "\n";
    std::cout << "Examples:\n";
    std::cout << "  " << program_name << " --port 8080 --debug\n";
    std::cout << "  " << program_name << " --host 0.0.0.0 --port 3000\n";
    std::cout << "\n";
}

/**
 * @brief 解析命令行参数
 */
ServerConfig parse_arguments(int argc, char* argv[]) {
    ServerConfig config;
    
    for (int i = 1; i < argc; ++i) {
        const std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            print_usage(argv[0]);
            std::exit(0);
        }
        else if (arg == "-p" || arg == "--port") {
            if (i + 1 < argc) {
                config.port = std::stoi(argv[++i]);
            } else {
                std::cerr << "Error: " << arg << " requires an argument\n";
                std::exit(1);
            }
        }
        else if (arg == "-H" || arg == "--host") {
            if (i + 1 < argc) {
                config.host = argv[++i];
            } else {
                std::cerr << "Error: " << arg << " requires an argument\n";
                std::exit(1);
            }
        }
        else if (arg == "-d" || arg == "--debug") {
            config.debug_mode = true;
            config.auth_manager.debug = true;
        }
        else if (arg == "--no-cors") {
            config.enable_cors = false;
        }
        else if (arg == "--session-path") {
            if (i + 1 < argc) {
                config.auth_manager.session_store_path = argv[++i];
            } else {
                std::cerr << "Error: " << arg << " requires an argument\n";
                std::exit(1);
            }
        }
        else if (arg == "--client-secret") {
            if (i + 1 < argc) {
                config.auth_manager.client_secret = argv[++i];
            } else {
                std::cerr << "Error: " << arg << " requires an argument\n";
                std::exit(1);
            }
        }
        else {
            std::cerr << "Error: unknown argument " << arg << "\n";
            print_usage(argv[0]);
            std::exit(1);
        }
    }
    
    return config;
}

/**
 * @brief 打印服务器信息
 */
void print_server_info(const AuthWebServer& server, const ServerConfig& config) {
    std::cout << "\n";
    std::cout << "🔐 Augment 认证服务器 (Authentication Web Server)\n";
    std::cout << "========================================\n";
    std::cout << "服务器地址: " << server.get_server_url() << "\n";
    std::cout << "调试模式: " << (config.debug_mode ? "已启用" : "已禁用") << "\n";
    std::cout << "CORS支持: " << (config.enable_cors ? "已启用" : "已禁用") << "\n";
    std::cout << "会话存储: " << config.auth_manager.session_store_path << "\n";
    std::cout << "客户端密钥: " << (config.auth_manager.client_secret.empty() ? "未设置" : "已设置") << "\n";
    std::cout << "\n";
    std::cout << "🌐 Web界面: " << server.get_server_url() << "\n";
    std::cout << "📡 API端点:\n";
    std::cout << "  GET  /api/start-auth      - 开始认证流程\n";
    std::cout << "  GET  /api/complete-auth   - 完成认证流程\n";
    std::cout << "  GET  /api/accounts        - 获取账户列表\n";
    std::cout << "  GET  /api/current-account - 获取当前账户\n";
    std::cout << "  GET  /api/switch-account  - 切换账户\n";
    std::cout << "  DELETE /api/remove-account - 删除账户\n";
    std::cout << "  GET  /api/refresh-token   - 刷新令牌\n";
    std::cout << "  GET  /callback            - OAuth回调\n";
    std::cout << "\n";
    std::cout << "💡 提示: 按 Ctrl+C 停止服务器\n";
    std::cout << "========================================\n\n";
}

/**
 * @brief 主函数
 */
int main(int argc, char* argv[]) {
    try {
        // 设置控制台编码以支持中文显示
        setup_console_encoding();

        // 解析命令行参数
        const auto config = parse_arguments(argc, argv);
        
        // 设置信号处理
        std::signal(SIGINT, signal_handler);
        std::signal(SIGTERM, signal_handler);
        
        // 创建服务器实例
        g_server = std::make_unique<AuthWebServer>(config);
        
        // 启动服务器
        std::cout << "🚀 正在启动 Augment 认证服务器...\n";
        auto start_future = g_server->start();
        
        // 等待服务器启动
        start_future.wait();
        
        // 打印服务器信息
        print_server_info(*g_server, config);
        
        // 保持服务器运行
        while (g_server->is_running()) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 服务器错误: " << e.what() << "\n";
        return 1;
    } catch (...) {
        std::cerr << "❌ 未知错误\n";
        return 1;
    }
    
    return 0;
}

/**
 * @brief 演示函数：展示如何使用API
 */
void demonstrate_api_usage() {
    std::cout << "\n=== API Usage Examples ===\n\n";

    std::cout << "1. Start authentication flow:\n";
    std::cout << "   curl http://localhost:3000/api/start-auth\n\n";

    std::cout << "2. Complete authentication flow:\n";
    std::cout << "   curl \"http://localhost:3000/api/complete-auth?callbackUrl=vscode://augment.vscode-augment/auth/result?code=...&state=...\"\n\n";
    
    std::cout << "3. Get account list:\n";
    std::cout << "   curl http://localhost:3000/api/accounts\n\n";

    std::cout << "4. Get current account:\n";
    std::cout << "   curl http://localhost:3000/api/current-account\n\n";

    std::cout << "5. Switch account:\n";
    std::cout << "   curl \"http://localhost:3000/api/switch-account?accountId=<EMAIL>\"\n\n";

    std::cout << "6. Refresh token:\n";
    std::cout << "   curl \"http://localhost:3000/api/refresh-token?accountId=<EMAIL>\"\n\n";

    std::cout << "7. Remove account:\n";
    std::cout << "   curl -X DELETE \"http://localhost:3000/api/remove-account?accountId=<EMAIL>\"\n\n";
}

/**
 * @brief 健康检查函数
 */
bool health_check(const std::string& server_url) {
    try {
        // 这里可以添加HTTP客户端代码来检查服务器健康状态
        // 暂时返回true作为占位符
        return true;
    } catch (...) {
        return false;
    }
}

/**
 * @brief 性能监控函数
 */
void monitor_performance() {
    // 这里可以添加性能监控代码
    // 例如：内存使用、CPU使用、请求统计等
}

/**
 * @brief 配置验证函数
 */
bool validate_config(const ServerConfig& config) {
    if (config.port < 1 || config.port > 65535) {
        std::cerr << "Error: port number must be between 1-65535\n";
        return false;
    }
    
    if (config.host.empty()) {
        std::cerr << "Error: host address cannot be empty\n";
        return false;
    }
    
    return true;
}
