/**
 * @file oauth_pkce_generator.cpp
 * @brief OAuth PKCE生成器实现
 */

#include "oauth_pkce_generator.hpp"
#include <random>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <regex>
#include <chrono>
#include <vector>
#include <array>
#include <cstdint>
#include <stdexcept>
#ifdef HAVE_OPENSSL
#include <openssl/sha.h>
#include <openssl/rand.h>
#endif

using namespace augment;
using json = nlohmann::json;

#ifndef HAVE_OPENSSL
// 简单的SHA256实现（仅用于测试，不推荐生产环境使用）
namespace {
    constexpr std::size_t SHA256_DIGEST_LENGTH = 32;

    // SHA256常量
    constexpr std::array<uint32_t, 64> K = {
        0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
        0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
        0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
        0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
        0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
        0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
        0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
        0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
    };

    uint32_t rotr(uint32_t x, int n) { return (x >> n) | (x << (32 - n)); }
    uint32_t ch(uint32_t x, uint32_t y, uint32_t z) { return (x & y) ^ (~x & z); }
    uint32_t maj(uint32_t x, uint32_t y, uint32_t z) { return (x & y) ^ (x & z) ^ (y & z); }
    uint32_t sigma0(uint32_t x) { return rotr(x, 2) ^ rotr(x, 13) ^ rotr(x, 22); }
    uint32_t sigma1(uint32_t x) { return rotr(x, 6) ^ rotr(x, 11) ^ rotr(x, 25); }
    uint32_t gamma0(uint32_t x) { return rotr(x, 7) ^ rotr(x, 18) ^ (x >> 3); }
    uint32_t gamma1(uint32_t x) { return rotr(x, 17) ^ rotr(x, 19) ^ (x >> 10); }

    void sha256_simple(const std::string& input, std::array<unsigned char, SHA256_DIGEST_LENGTH>& hash) {
        // 初始哈希值
        std::array<uint32_t, 8> h = {
            0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a,
            0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19
        };

        // 预处理
        std::vector<unsigned char> msg(input.begin(), input.end());
        uint64_t msg_len = msg.size();
        msg.push_back(0x80);

        while ((msg.size() % 64) != 56) {
            msg.push_back(0x00);
        }

        // 添加长度
        for (int i = 7; i >= 0; --i) {
            msg.push_back((msg_len * 8) >> (i * 8));
        }

        // 处理消息块
        for (std::size_t chunk = 0; chunk < msg.size(); chunk += 64) {
            std::array<uint32_t, 64> w{};

            // 复制块到w[0..15]
            for (int i = 0; i < 16; ++i) {
                w[i] = (static_cast<uint32_t>(msg[chunk + i * 4]) << 24) |
                       (static_cast<uint32_t>(msg[chunk + i * 4 + 1]) << 16) |
                       (static_cast<uint32_t>(msg[chunk + i * 4 + 2]) << 8) |
                       static_cast<uint32_t>(msg[chunk + i * 4 + 3]);
            }

            // 扩展到w[16..63]
            for (int i = 16; i < 64; ++i) {
                w[i] = gamma1(w[i - 2]) + w[i - 7] + gamma0(w[i - 15]) + w[i - 16];
            }

            // 初始化工作变量
            auto [a, b, c, d, e, f, g, h_var] = h;

            // 主循环
            for (int i = 0; i < 64; ++i) {
                uint32_t t1 = h_var + sigma1(e) + ch(e, f, g) + K[i] + w[i];
                uint32_t t2 = sigma0(a) + maj(a, b, c);
                h_var = g;
                g = f;
                f = e;
                e = d + t1;
                d = c;
                c = b;
                b = a;
                a = t1 + t2;
            }

            // 添加到哈希值
            h[0] += a; h[1] += b; h[2] += c; h[3] += d;
            h[4] += e; h[5] += f; h[6] += g; h[7] += h_var;
        }

        // 产生最终哈希值
        for (int i = 0; i < 8; ++i) {
            hash[i * 4] = h[i] >> 24;
            hash[i * 4 + 1] = h[i] >> 16;
            hash[i * 4 + 2] = h[i] >> 8;
            hash[i * 4 + 3] = h[i];
        }
    }
}
#endif

// OAuthPKCEGenerator 实现
OAuthPKCEGenerator::OAuthPKCEGenerator(const OAuthConfig& config)
    : config_(config) {
}

std::string OAuthPKCEGenerator::generate_random_string(std::size_t length) {
    if (length < 43 || length > 128) {
        throw PKCEException("Code verifier长度必须在43-128字符之间");
    }
    
    // RFC 7636兼容的字符集
    static constexpr char charset[] = 
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";
    static constexpr std::size_t charset_size = sizeof(charset) - 1;
    
    std::string result;
    result.reserve(length);
    
#ifdef HAVE_OPENSSL
    // 使用OpenSSL生成加密安全的随机数
    std::vector<unsigned char> random_bytes(length);
    if (RAND_bytes(random_bytes.data(), static_cast<int>(length)) != 1) {
        throw PKCEException("生成随机数失败");
    }

    for (std::size_t i = 0; i < length; ++i) {
        result += charset[random_bytes[i] % charset_size];
    }
#else
    // 使用标准库随机数生成器（非加密安全，仅用于测试）
    static thread_local std::random_device rd;
    static thread_local std::mt19937 gen(rd());
    std::uniform_int_distribution<int> dis(0, static_cast<int>(charset_size - 1));

    for (std::size_t i = 0; i < length; ++i) {
        result += charset[dis(gen)];
    }
#endif
    
    return result;
}

std::string OAuthPKCEGenerator::base64url_encode(std::string_view data) {
    // Base64编码表
    static constexpr char base64_chars[] =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    
    std::string result;
    result.reserve(((data.size() + 2) / 3) * 4);
    
    for (std::size_t i = 0; i < data.size(); i += 3) {
        const std::uint32_t triple = 
            (static_cast<std::uint32_t>(static_cast<unsigned char>(data[i])) << 16) |
            (i + 1 < data.size() ? static_cast<std::uint32_t>(static_cast<unsigned char>(data[i + 1])) << 8 : 0) |
            (i + 2 < data.size() ? static_cast<std::uint32_t>(static_cast<unsigned char>(data[i + 2])) : 0);
        
        result += base64_chars[(triple >> 18) & 0x3F];
        result += base64_chars[(triple >> 12) & 0x3F];
        result += (i + 1 < data.size()) ? base64_chars[(triple >> 6) & 0x3F] : '=';
        result += (i + 2 < data.size()) ? base64_chars[triple & 0x3F] : '=';
    }
    
    // 转换为Base64URL：替换字符并移除填充
    std::replace(result.begin(), result.end(), '+', '-');
    std::replace(result.begin(), result.end(), '/', '_');
    
    // 移除末尾的'='填充字符
    result.erase(std::find(result.begin(), result.end(), '='), result.end());
    
    return result;
}

std::string OAuthPKCEGenerator::generate_code_challenge(const std::string& code_verifier) {
    if (!is_valid_code_verifier(code_verifier)) {
        throw PKCEException("无效的code verifier格式");
    }
    
    // 生成SHA256哈希
    std::array<unsigned char, SHA256_DIGEST_LENGTH> hash{};

#ifdef HAVE_OPENSSL
    SHA256_CTX sha256_ctx;
    SHA256_Init(&sha256_ctx);
    SHA256_Update(&sha256_ctx, code_verifier.c_str(), code_verifier.length());
    SHA256_Final(hash.data(), &sha256_ctx);
#else
    sha256_simple(code_verifier, hash);
#endif

    // 转换为Base64URL编码
    const std::string_view hash_view(reinterpret_cast<const char*>(hash.data()), hash.size());
    return base64url_encode(hash_view);
}

PKCEParams OAuthPKCEGenerator::generate_pkce_params() {
    PKCEParams params;
    
    // 生成code_verifier（128字符以获得最大熵）
    params.code_verifier = generate_random_string(128);
    
    // 从code_verifier生成code_challenge
    params.code_challenge = generate_code_challenge(params.code_verifier);
    
    return params;
}

std::string OAuthPKCEGenerator::generate_state() {
    // 生成UUID v4格式的state
    std::array<unsigned char, 16> uuid_bytes{};

#ifdef HAVE_OPENSSL
    if (RAND_bytes(uuid_bytes.data(), 16) != 1) {
        throw PKCEException("生成UUID失败");
    }
#else
    // 使用标准库随机数生成器
    static thread_local std::random_device rd;
    static thread_local std::mt19937 gen(rd());
    std::uniform_int_distribution<int> dis(0, 255);

    for (auto& byte : uuid_bytes) {
        byte = static_cast<unsigned char>(dis(gen));
    }
#endif
    
    // 设置版本和变体位
    uuid_bytes[6] = (uuid_bytes[6] & 0x0F) | 0x40; // 版本4
    uuid_bytes[8] = (uuid_bytes[8] & 0x3F) | 0x80; // 变体10
    
    std::ostringstream oss;
    oss << std::hex << std::setfill('0');
    
    for (std::size_t i = 0; i < 16; ++i) {
        if (i == 4 || i == 6 || i == 8 || i == 10) {
            oss << '-';
        }
        oss << std::setw(2) << static_cast<unsigned int>(uuid_bytes[i]);
    }
    
    return oss.str();
}

AuthorizationURLData OAuthPKCEGenerator::generate_authorization_url(
    const std::unordered_map<std::string, std::string>& custom_params) const {
    
    // 生成state参数
    const std::string state = generate_state();
    
    // 生成PKCE参数
    const auto pkce_params = generate_pkce_params();
    
    // 构建参数映射
    std::unordered_map<std::string, std::string> params = {
        {"response_type", config_.response_type},
        {"code_challenge", pkce_params.code_challenge},
        {"code_challenge_method", config_.code_challenge_method},
        {"client_id", config_.client_id},
        {"redirect_uri", config_.redirect_uri},
        {"state", state},
        {"scope", config_.scope},
        {"prompt", config_.prompt}
    };
    
    // 添加自定义参数
    for (const auto& [key, value] : config_.custom_params) {
        params[key] = value;
    }
    
    // 覆盖自定义参数
    for (const auto& [key, value] : custom_params) {
        params[key] = value;
    }
    
    // 构建完整URL
    const std::string query_string = build_query_string(params);
    const std::string full_url = config_.auth_base_url + "?" + query_string;
    
    return {
        full_url,
        pkce_params.code_verifier,
        state,
        pkce_params.code_challenge
    };
}

bool OAuthPKCEGenerator::validate_parameters(const std::string& code_challenge, 
                                            const std::string& state) {
    return is_valid_code_challenge(code_challenge) && is_valid_state(state);
}

CallbackData OAuthPKCEGenerator::parse_callback_url(const std::string& callback_url) {
    // 解析URL
    const std::regex url_regex(R"(^([^:/?#]+):(?://([^/?#]*))?([^?#]*)(?:\?([^#]*))?(?:#(.*))?$)");
    std::smatch url_match;
    
    if (!std::regex_match(callback_url, url_match, url_regex)) {
        throw PKCEException("无效的回调URL格式");
    }
    
    const std::string query_string = url_match[4].str();
    const auto params = parse_query_string(query_string);
    
    CallbackData result;
    
    // 提取必需参数
    auto it = params.find("code");
    if (it != params.end()) {
        result.code = it->second;
    }
    
    it = params.find("state");
    if (it != params.end()) {
        result.state = it->second;
    }
    
    it = params.find("tenant_url");
    if (it != params.end()) {
        result.tenant_url = url_decode(it->second);
    }
    
    // 提取错误参数
    it = params.find("error");
    if (it != params.end()) {
        result.error = it->second;
    }
    
    it = params.find("error_description");
    if (it != params.end()) {
        result.error_description = url_decode(it->second);
    }
    
    return result;
}

bool OAuthPKCEGenerator::validate_state(const std::string& received_state, 
                                       const std::string& original_state) {
    return received_state == original_state;
}

// 私有方法实现
std::string OAuthPKCEGenerator::build_query_string(
    const std::unordered_map<std::string, std::string>& params) {
    
    std::ostringstream oss;
    bool first = true;
    
    for (const auto& [key, value] : params) {
        if (!first) {
            oss << '&';
        }
        oss << url_encode(key) << '=' << url_encode(value);
        first = false;
    }
    
    return oss.str();
}

std::string OAuthPKCEGenerator::url_encode(const std::string& str) {
    std::ostringstream oss;
    oss << std::hex << std::uppercase << std::setfill('0');
    
    for (unsigned char c : str) {
        if (std::isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
            oss << c;
        } else {
            oss << '%' << std::setw(2) << static_cast<unsigned int>(c);
        }
    }
    
    return oss.str();
}

std::string OAuthPKCEGenerator::url_decode(const std::string& str) {
    std::string result;
    result.reserve(str.length());
    
    for (std::size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '%' && i + 2 < str.length()) {
            const std::string hex = str.substr(i + 1, 2);
            const auto value = static_cast<char>(std::stoi(hex, nullptr, 16));
            result += value;
            i += 2;
        } else if (str[i] == '+') {
            result += ' ';
        } else {
            result += str[i];
        }
    }
    
    return result;
}

std::unordered_map<std::string, std::string> 
OAuthPKCEGenerator::parse_query_string(const std::string& query_string) {
    std::unordered_map<std::string, std::string> params;
    
    if (query_string.empty()) {
        return params;
    }
    
    std::istringstream iss(query_string);
    std::string pair;
    
    while (std::getline(iss, pair, '&')) {
        const auto eq_pos = pair.find('=');
        if (eq_pos != std::string::npos) {
            const std::string key = url_decode(pair.substr(0, eq_pos));
            const std::string value = url_decode(pair.substr(eq_pos + 1));
            params[key] = value;
        }
    }
    
    return params;
}

bool OAuthPKCEGenerator::is_valid_code_verifier(const std::string& code_verifier) {
    // RFC 7636: 43-128字符，URL安全字符集
    if (code_verifier.length() < 43 || code_verifier.length() > 128) {
        return false;
    }
    
    static const std::regex verifier_regex(R"([A-Za-z0-9\-\._~]+)");
    return std::regex_match(code_verifier, verifier_regex);
}

bool OAuthPKCEGenerator::is_valid_code_challenge(const std::string& code_challenge) {
    // Base64URL编码的SHA256哈希应该是43字符
    if (code_challenge.length() != 43) {
        return false;
    }
    
    static const std::regex challenge_regex(R"([A-Za-z0-9\-_]+)");
    return std::regex_match(code_challenge, challenge_regex);
}

bool OAuthPKCEGenerator::is_valid_state(const std::string& state) {
    // 支持UUID格式或自定义格式
    if (state.empty()) {
        return false;
    }
    
    // UUID v4格式检查
    static const std::regex uuid_regex(
        R"([0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12})",
        std::regex_constants::icase);
    
    if (std::regex_match(state, uuid_regex)) {
        return true;
    }
    
    // 自定义格式：至少3个字符，只包含字母数字和安全字符
    if (state.length() >= 3) {
        static const std::regex custom_regex(R"([A-Za-z0-9\-_\.~]+)");
        return std::regex_match(state, custom_regex);
    }
    
    return false;
}

// 便利函数实现
AuthorizationURLData generate_oauth_url(const OAuthConfig& config) {
    OAuthPKCEGenerator generator(config);
    return generator.generate_authorization_url();
}
