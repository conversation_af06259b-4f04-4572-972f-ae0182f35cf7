/**
 * @file auth_web_server.cpp
 * @brief Augment Authentication Web Server Implementation
 */

#include "auth_web_server.hpp"
#include "augment_auth_manager.hpp"
#include <iostream>
#include <sstream>
#include <thread>
#include <iomanip>
#include <ctime>

using namespace augment;
using json = nlohmann::json;

// AuthWebServer Implementation
AuthWebServer::AuthWebServer(const ServerConfig& config)
    : config_(config), server_(std::make_unique<httplib::Server>()) {
    
    // 创建认证管理器
    AugmentAuthManagerBuilder builder;
    auth_manager_ = builder
        .debug(config.auth_manager.debug)
        .client_secret(config.auth_manager.client_secret)
        .session_store_path(config.auth_manager.session_store_path)
        .build();
    
    // Set server configuration
    server_->set_read_timeout(config.request_timeout);
    server_->set_write_timeout(config.request_timeout);
    
    // Set up routes
    setup_routes();
}

AuthWebServer::~AuthWebServer() {
    if (running_.load()) {
        auto stop_future = stop();
        stop_future.wait();
    }
}

std::future<void> AuthWebServer::start() {
    return std::async(std::launch::async, [this]() {
        std::lock_guard<std::mutex> lock(server_mutex_);
        
        if (running_.load()) {
            throw std::runtime_error("Server is already running");
        }
        
        // Initialize authentication manager
        auto init_future = auth_manager_->initialize();
        init_future.get();
        
        // Start HTTP server
        running_.store(true);
        
        std::cout << "Starting Web server: " << config_.host << ":" << config_.port << std::endl;
        
        // Start server in new thread
        std::thread server_thread([this]() {
            if (!server_->listen(config_.host.c_str(), config_.port)) {
                running_.store(false);
                throw std::runtime_error("Unable to start HTTP server");
            }
        });
        
        server_thread.detach();
        
        // Wait for server to start
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        if (!running_.load()) {
            throw std::runtime_error("Server startup failed");
        }
    });
}

std::future<void> AuthWebServer::stop() {
    return std::async(std::launch::async, [this]() {
        std::lock_guard<std::mutex> lock(server_mutex_);
        
        if (!running_.load()) {
            return;
        }
        
        std::cout << "Stopping Web server..." << std::endl;
        
        server_->stop();
        running_.store(false);
        
        std::cout << "Web server stopped" << std::endl;
    });
}

bool AuthWebServer::is_running() const noexcept {
    return running_.load();
}

std::string AuthWebServer::get_server_url() const {
    return "http://" + config_.host + ":" + std::to_string(config_.port);
}

void AuthWebServer::set_custom_handler(const std::string& path, 
                                      std::function<void(const httplib::Request&, httplib::Response&)> handler) {
    server_->Get(path.c_str(), wrap_handler(handler));
}

// Private method implementations
void AuthWebServer::setup_routes() {
    // Root path - Web interface
    server_->Get("/", wrap_handler([this](const httplib::Request& req, httplib::Response& res) {
        handle_web_interface(req, res);
    }));
    
    // API routes
    server_->Get("/api/start-auth", wrap_handler([this](const httplib::Request& req, httplib::Response& res) {
        handle_start_auth(req, res);
    }));

    server_->Get("/api/complete-auth", wrap_handler([this](const httplib::Request& req, httplib::Response& res) {
        handle_complete_auth(req, res);
    }));

    server_->Get("/api/accounts", wrap_handler([this](const httplib::Request& req, httplib::Response& res) {
        handle_accounts(req, res);
    }));

    server_->Get("/api/current-account", wrap_handler([this](const httplib::Request& req, httplib::Response& res) {
        handle_current_account(req, res);
    }));

    server_->Get("/api/switch-account", wrap_handler([this](const httplib::Request& req, httplib::Response& res) {
        handle_switch_account(req, res);
    }));

    server_->Delete("/api/remove-account", wrap_handler([this](const httplib::Request& req, httplib::Response& res) {
        handle_remove_account(req, res);
    }));

    server_->Get("/api/refresh-token", wrap_handler([this](const httplib::Request& req, httplib::Response& res) {
        handle_refresh_token(req, res);
    }));
    
    server_->Get("/callback", wrap_handler([this](const httplib::Request& req, httplib::Response& res) {
        handle_oauth_callback(req, res);
    }));
    
    // OPTIONS handling (CORS preflight)
    server_->Options(".*", wrap_handler([this](const httplib::Request& req, httplib::Response& res) {
        handle_options(req, res);
    }));
}

void AuthWebServer::set_cors_headers(httplib::Response& res) const {
    if (config_.enable_cors) {
        // 只设置CORS相关头部，避免任何可能与Content-Length冲突的头部
        res.set_header("Access-Control-Allow-Origin", "*");
        res.set_header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        res.set_header("Access-Control-Allow-Headers", "Content-Type, Authorization");
        res.set_header("Access-Control-Max-Age", "86400");
    }
}

void AuthWebServer::send_json_response(httplib::Response& res,
                                      const nlohmann::json& json_data,
                                      int status_code) const {
    // 设置状态码
    res.status = status_code;

    // 生成JSON字符串
    std::string json_str = json_data.dump(2);

    // 设置内容（httplib会自动设置Content-Length）
    res.set_content(json_str, "application/json; charset=utf-8");
}

void AuthWebServer::send_error_response(httplib::Response& res, 
                                       int status_code, 
                                       const std::string& message) const {
    json error_json;
    error_json["error"] = true;
    error_json["message"] = message;
    error_json["status"] = status_code;
    
    send_json_response(res, error_json, status_code);
}

nlohmann::json AuthWebServer::parse_request_json(const httplib::Request& req) const {
    if (req.body.empty()) {
        throw HttpException(400, "Request body is empty");
    }

    try {
        return json::parse(req.body);
    } catch (const std::exception& e) {
        throw HttpException(400, "JSON parsing failed: " + std::string(e.what()));
    }
}

std::string AuthWebServer::get_query_param(const httplib::Request& req, const std::string& key) const {
    auto it = req.headers.find(key);
    if (it != req.headers.end()) {
        return it->second;
    }

    // Parse query string manually since httplib doesn't provide query params
    std::string query = req.path;
    size_t query_pos = query.find('?');
    if (query_pos == std::string::npos) {
        return "";
    }

    query = query.substr(query_pos + 1);
    std::string search_key = key + "=";
    size_t key_pos = query.find(search_key);
    if (key_pos == std::string::npos) {
        return "";
    }

    size_t value_start = key_pos + search_key.length();
    size_t value_end = query.find('&', value_start);
    if (value_end == std::string::npos) {
        value_end = query.length();
    }

    return query.substr(value_start, value_end - value_start);
}

// Route handler implementations
void AuthWebServer::handle_web_interface(const httplib::Request& req, httplib::Response& res) {
    // 生成完整的HTML界面
    const std::string html = generate_web_interface_html();

    res.set_content(html, "text/html; charset=utf-8");
    res.status = 200;
}

void AuthWebServer::handle_start_auth(const httplib::Request& req, httplib::Response& res) {
    try {
        json options;
        if (!req.body.empty()) {
            options = parse_request_json(req);
        }
        
        auto flow_future = auth_manager_->start_auth_flow(options);
        auto flow_data = flow_future.get();
        
        json response;
        response["flowId"] = flow_data.flow_id;
        response["authUrl"] = flow_data.auth_url;
        response["state"] = flow_data.state;
        response["codeVerifier"] = flow_data.code_verifier;
        response["codeChallenge"] = flow_data.code_challenge;
        response["instructions"] = flow_data.instructions;
        
        send_json_response(res, response);
        
    } catch (const std::exception& e) {
        send_error_response(res, 500, e.what());
    }
}

void AuthWebServer::handle_complete_auth(const httplib::Request& req, httplib::Response& res) {
    try {
        const std::string callback_url = get_query_param(req, "callbackUrl");

        if (callback_url.empty()) {
            throw HttpException(400, "Missing callbackUrl parameter");
        }
        
        auto result_future = auth_manager_->complete_auth_flow(callback_url);
        auto result = result_future.get();
        
        json response;
        response["success"] = result.success;
        response["accountId"] = result.account_id;
        response["email"] = result.email;
        response["tenantUrl"] = result.tenant_url;
        
        send_json_response(res, response);
        
    } catch (const HttpException& e) {
        send_error_response(res, e.status_code(), e.what());
    } catch (const std::exception& e) {
        send_error_response(res, 500, e.what());
    }
}

void AuthWebServer::handle_accounts(const httplib::Request& req, httplib::Response& res) {
    try {
        auto accounts_future = auth_manager_->list_accounts();
        auto accounts = accounts_future.get();

        json response = json::array();

        for (const auto& account : accounts) {
            json account_json;
            account_json["accountId"] = account.account_id;
            account_json["email"] = account.email;
            account_json["tenantUrl"] = account.tenant_url;
            account_json["tokenType"] = account.token_type;
            account_json["scope"] = account.scope;
            account_json["isExpired"] = account.is_expired;

            // Convert timestamp to ISO string format (matching original JS)
            auto time_t = std::chrono::system_clock::to_time_t(account.last_used);
            std::ostringstream oss;
            oss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%S.000Z");
            account_json["lastUsed"] = oss.str();

            if (account.expires_at) {
                auto expires_time_t = std::chrono::system_clock::to_time_t(*account.expires_at);
                std::ostringstream expires_oss;
                expires_oss << std::put_time(std::gmtime(&expires_time_t), "%Y-%m-%dT%H:%M:%S.000Z");
                account_json["expiresAt"] = expires_oss.str();
            }

            response.push_back(account_json);
        }

        send_json_response(res, response);

    } catch (const std::exception& e) {
        send_error_response(res, 500, e.what());
    }
}

void AuthWebServer::handle_current_account(const httplib::Request& req, httplib::Response& res) {
    try {
        auto current_future = auth_manager_->get_current_account();
        auto current_account = current_future.get();

        json response;

        if (current_account) {
            const auto& account = *current_account;
            response["accountId"] = account.account_id;
            response["email"] = account.email;
            response["tenantUrl"] = account.tenant_url;
            response["tokenType"] = account.token_type;
            response["scope"] = account.scope;
            response["isExpired"] = account.is_expired;

            // Convert timestamp to ISO string format
            auto time_t = std::chrono::system_clock::to_time_t(account.last_used);
            std::ostringstream oss;
            oss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%S.000Z");
            response["lastUsed"] = oss.str();

            if (account.expires_at) {
                auto expires_time_t = std::chrono::system_clock::to_time_t(*account.expires_at);
                std::ostringstream expires_oss;
                expires_oss << std::put_time(std::gmtime(&expires_time_t), "%Y-%m-%dT%H:%M:%S.000Z");
                response["expiresAt"] = expires_oss.str();
            }
        } else {
            response = nullptr;
        }

        send_json_response(res, response);
        
    } catch (const std::exception& e) {
        send_error_response(res, 500, e.what());
    }
}

void AuthWebServer::handle_switch_account(const httplib::Request& req, httplib::Response& res) {
    try {
        const std::string account_id = get_query_param(req, "accountId");

        if (account_id.empty()) {
            throw HttpException(400, "Missing accountId parameter");
        }
        
        auto account_future = auth_manager_->switch_account(account_id);
        auto account = account_future.get();
        
        json response;
        response["success"] = true;
        json account_json = json::object();
        account_json["accountId"] = account.account_id;
        account_json["email"] = account.email;
        account_json["tenantUrl"] = account.tenant_url;
        account_json["tokenType"] = account.token_type;
        account_json["scope"] = account.scope;
        account_json["isExpired"] = account.is_expired;
        response["account"] = account_json;
        
        send_json_response(res, response);
        
    } catch (const HttpException& e) {
        send_error_response(res, e.status_code(), e.what());
    } catch (const std::exception& e) {
        send_error_response(res, 500, e.what());
    }
}

void AuthWebServer::handle_remove_account(const httplib::Request& req, httplib::Response& res) {
    try {
        const std::string account_id = get_query_param(req, "accountId");

        if (account_id.empty()) {
            throw HttpException(400, "Missing accountId parameter");
        }
        
        auto remove_future = auth_manager_->remove_account(account_id);
        remove_future.get();
        
        json response;
        response["success"] = true;
        response["message"] = "Account removed";
        
        send_json_response(res, response);
        
    } catch (const HttpException& e) {
        send_error_response(res, e.status_code(), e.what());
    } catch (const std::exception& e) {
        send_error_response(res, 500, e.what());
    }
}

void AuthWebServer::handle_refresh_token(const httplib::Request& req, httplib::Response& res) {
    try {
        const std::string account_id = get_query_param(req, "accountId");

        if (account_id.empty()) {
            throw HttpException(400, "Missing accountId parameter");
        }
        
        auto refresh_future = auth_manager_->refresh_account_token(account_id);
        bool success = refresh_future.get();
        
        json response;
        response["success"] = success;
        response["message"] = success ? "Token refresh successful" : "Token refresh failed";
        
        send_json_response(res, response);
        
    } catch (const HttpException& e) {
        send_error_response(res, e.status_code(), e.what());
    } catch (const std::exception& e) {
        send_error_response(res, 500, e.what());
    }
}

void AuthWebServer::handle_oauth_callback(const httplib::Request& req, httplib::Response& res) {
    // 设置状态码
    res.status = 200;

    // 先设置CORS头
    set_cors_headers(res);

    // OAuth callback is usually handled by browser, provide a simple response here
    const std::string html = R"(<!DOCTYPE html>
<html>
<head>
    <title>Authentication Complete</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Authentication Complete</h1>
    <p>You can close this window and return to VSCode.</p>
    <script>
        // Try to close window
        setTimeout(() => {
            window.close();
        }, 2000);
    </script>
</body>
</html>)";

    // 最后设置内容（httplib会自动设置Content-Length）
    res.set_content(html, "text/html; charset=utf-8");
}

void AuthWebServer::handle_options(const httplib::Request& req, httplib::Response& res) {
    set_cors_headers(res);
    res.status = 200;
}

std::string AuthWebServer::generate_web_interface_html() const {
    // 基于原始JavaScript项目的完整HTML界面
    return R"(<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment Authentication Manager</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #fafafa; }
        .section h3 { margin-top: 0; color: #555; }
        button { background: #007acc; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a9e; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .account-item { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border: 1px solid #ddd; }
        .current-account { border-color: #007acc; background: #f0f8ff; }
        .status { padding: 4px 8px; border-radius: 3px; font-size: 12px; font-weight: bold; }
        .status.valid { background: #d4edda; color: #155724; }
        .status.expired { background: #f8d7da; color: #721c24; }
        .auth-url { background: #f8f9fa; padding: 15px; border-radius: 5px; word-break: break-all; margin: 10px 0; }
        .callback-input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin: 10px 0; }
        .hidden { display: none; }
        .loading { opacity: 0.6; pointer-events: none; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Augment Authentication Manager</h1>

        <div class="section">
            <h3>🚀 Quick Start</h3>
            <button onclick="startAuth()">Start New Authentication</button>
            <button onclick="refreshAccounts()">Refresh Account List</button>

            <div id="authFlow" class="hidden">
                <h4>Step 1: Open Authorization URL</h4>
                <div id="authUrl" class="auth-url"></div>
                <button onclick="openAuthUrl()">Open in Browser</button>

                <h4>Step 2: Complete Authentication</h4>
                <input type="text" id="callbackUrl" class="callback-input" placeholder="Paste the vscode:// callback URL here">
                <button onclick="completeAuth()">Complete Authentication</button>
            </div>
        </div>

        <div class="section">
            <h3>👤 Current Account</h3>
            <div id="currentAccount">Loading...</div>
        </div>

        <div class="section">
            <h3>📋 All Accounts</h3>
            <div id="accountsList">Loading...</div>
        </div>

        <div class="section">
            <h3>📊 Status</h3>
            <div id="status"></div>
        </div>
    </div>

    <script>
        let currentAuthFlow = null;

        async function apiCall(endpoint, options = {}) {
            try {
                const response = await fetch(endpoint, {
                    headers: { 'Content-Type': 'application/json' },
                    ...options
                });
                const data = await response.json();
                if (!response.ok) throw new Error(data.error || 'API call failed');
                return data;
            } catch (error) {
                showStatus('Error: ' + error.message, 'error');
                throw error;
            }
        }

        async function startAuth() {
            showStatus('Starting authentication flow...', 'loading');
            try {
                // 注意：C++版本使用GET而不是POST
                const result = await apiCall('/api/start-auth');
                currentAuthFlow = result;

                document.getElementById('authUrl').textContent = result.authUrl;
                document.getElementById('authFlow').classList.remove('hidden');

                showStatus('Authentication flow started. Please follow the steps above.', 'success');
            } catch (error) {
                showStatus('Failed to start authentication: ' + error.message, 'error');
            }
        }

        function openAuthUrl() {
            if (currentAuthFlow) {
                window.open(currentAuthFlow.authUrl, '_blank');
            }
        }

        async function completeAuth() {
            const callbackUrl = document.getElementById('callbackUrl').value.trim();
            if (!callbackUrl) {
                showStatus('Please paste the callback URL', 'error');
                return;
            }

            showStatus('Completing authentication...', 'loading');
            try {
                // 注意：C++版本使用GET参数而不是POST body
                const result = await apiCall('/api/complete-auth?callbackUrl=' + encodeURIComponent(callbackUrl));

                document.getElementById('authFlow').classList.add('hidden');
                document.getElementById('callbackUrl').value = '';
                currentAuthFlow = null;

                showStatus(`Authentication completed for ${result.email}`, 'success');
                await refreshAccounts();
                await refreshCurrentAccount();
            } catch (error) {
                showStatus('Authentication failed: ' + error.message, 'error');
            }
        }

        async function refreshAccounts() {
            try {
                const accounts = await apiCall('/api/accounts');
                displayAccounts(accounts);
            } catch (error) {
                document.getElementById('accountsList').innerHTML = '<p class="error">Failed to load accounts</p>';
            }
        }

        async function refreshCurrentAccount() {
            try {
                const current = await apiCall('/api/current-account');
                displayCurrentAccount(current);
            } catch (error) {
                document.getElementById('currentAccount').innerHTML = '<p class="error">Failed to load current account</p>';
            }
        }

        function displayAccounts(accounts) {
            const container = document.getElementById('accountsList');
            if (accounts.length === 0) {
                container.innerHTML = '<p>No accounts found. Start authentication to add an account.</p>';
                return;
            }

            container.innerHTML = accounts.map(account => `
                <div class="account-item">
                    <strong>${account.email || account.accountId}</strong>
                    <span class="status ${account.isExpired ? 'expired' : 'valid'}">${account.isExpired ? 'Expired' : 'Valid'}</span>
                    <br>
                    <small>ID: ${account.accountId}</small><br>
                    <small>Tenant: ${account.tenantUrl}</small><br>
                    <small>Last Used: ${new Date(account.lastUsed).toLocaleString()}</small>
                    <div style="margin-top: 10px;">
                        <button onclick="switchAccount('${account.accountId}')">Switch To</button>
                        <button onclick="refreshToken('${account.accountId}')">Refresh Token</button>
                        <button onclick="removeAccount('${account.accountId}')" style="background: #dc3545;">Remove</button>
                    </div>
                </div>
            `).join('');
        }

        function displayCurrentAccount(current) {
            const container = document.getElementById('currentAccount');
            if (!current) {
                container.innerHTML = '<p>No current account. Please authenticate first.</p>';
                return;
            }

            container.innerHTML = `
                <div class="account-item current-account">
                    <strong>👤 ${current.email || current.accountId}</strong>
                    <span class="status ${current.isExpired ? 'expired' : 'valid'}">${current.isExpired ? 'Expired' : 'Valid'}</span>
                    <br>
                    <small>ID: ${current.accountId}</small><br>
                    <small>Tenant: ${current.tenantUrl}</small>
                </div>
            `;
        }

        async function switchAccount(accountId) {
            showStatus(`Switching to account ${accountId}...`, 'loading');
            try {
                // 注意：C++版本使用GET参数而不是POST body
                await apiCall('/api/switch-account?accountId=' + encodeURIComponent(accountId));
                showStatus('Account switched successfully', 'success');
                await refreshCurrentAccount();
            } catch (error) {
                showStatus('Failed to switch account: ' + error.message, 'error');
            }
        }

        async function refreshToken(accountId) {
            showStatus(`Refreshing token for ${accountId}...`, 'loading');
            try {
                // 注意：C++版本使用GET参数而不是POST body
                await apiCall('/api/refresh-token?accountId=' + encodeURIComponent(accountId));
                showStatus('Token refreshed successfully', 'success');
                await refreshAccounts();
            } catch (error) {
                showStatus('Failed to refresh token: ' + error.message, 'error');
            }
        }

        async function removeAccount(accountId) {
            if (!confirm(`Are you sure you want to remove account ${accountId}?`)) return;

            showStatus(`Removing account ${accountId}...`, 'loading');
            try {
                // 注意：C++版本使用DELETE with GET参数
                await apiCall('/api/remove-account?accountId=' + encodeURIComponent(accountId), { method: 'DELETE' });
                showStatus('Account removed successfully', 'success');
                await refreshAccounts();
                await refreshCurrentAccount();
            } catch (error) {
                showStatus('Failed to remove account: ' + error.message, 'error');
            }
        }

        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<p class="${type}">${message}</p>`;

            if (type === 'loading') {
                document.body.classList.add('loading');
            } else {
                document.body.classList.remove('loading');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            refreshAccounts();
            refreshCurrentAccount();
        });
    </script>
</body>
</html>)";
}



std::function<void(const httplib::Request&, httplib::Response&)>
AuthWebServer::wrap_handler(std::function<void(const httplib::Request&, httplib::Response&)> handler) {
    return [this, handler](const httplib::Request& req, httplib::Response& res) {
        try {
            handler(req, res);
        } catch (const HttpException& e) {
            send_error_response(res, e.status_code(), e.what());
        } catch (const std::exception& e) {
            send_error_response(res, 500, e.what());
        }
    };
}
