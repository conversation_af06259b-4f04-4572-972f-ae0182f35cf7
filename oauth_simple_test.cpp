/**
 * @file oauth_simple_test.cpp
 * @brief 简单的OAuth PKCE测试程序 - 不依赖外部库
 */

#include <iostream>
#include <string>
#include <random>
#include <sstream>
#include <iomanip>
#include <algorithm>

// 简单的Base64URL编码实现
std::string base64url_encode_simple(const std::string& data) {
    // Base64编码表
    static constexpr char base64_chars[] =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    
    std::string result;
    result.reserve(((data.size() + 2) / 3) * 4);
    
    for (std::size_t i = 0; i < data.size(); i += 3) {
        const std::uint32_t triple = 
            (static_cast<std::uint32_t>(static_cast<unsigned char>(data[i])) << 16) |
            (i + 1 < data.size() ? static_cast<std::uint32_t>(static_cast<unsigned char>(data[i + 1])) << 8 : 0) |
            (i + 2 < data.size() ? static_cast<std::uint32_t>(static_cast<unsigned char>(data[i + 2])) : 0);
        
        result += base64_chars[(triple >> 18) & 0x3F];
        result += base64_chars[(triple >> 12) & 0x3F];
        result += (i + 1 < data.size()) ? base64_chars[(triple >> 6) & 0x3F] : '=';
        result += (i + 2 < data.size()) ? base64_chars[triple & 0x3F] : '=';
    }
    
    // 转换为Base64URL：替换字符并移除填充
    std::replace(result.begin(), result.end(), '+', '-');
    std::replace(result.begin(), result.end(), '/', '_');
    
    // 移除末尾的'='填充字符
    result.erase(std::find(result.begin(), result.end(), '='), result.end());
    
    return result;
}

// 简单的随机字符串生成
std::string generate_random_string(std::size_t length) {
    static constexpr char charset[] = 
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";
    static constexpr std::size_t charset_size = sizeof(charset) - 1;
    
    std::string result;
    result.reserve(length);
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, charset_size - 1);
    
    for (std::size_t i = 0; i < length; ++i) {
        result += charset[dis(gen)];
    }
    
    return result;
}

// 简化的哈希函数（仅用于演示）
std::string simple_hash(const std::string& input) {
    std::hash<std::string> hasher;
    auto hash_value = hasher(input);
    
    std::ostringstream oss;
    oss << std::hex << hash_value;
    std::string result = oss.str();
    
    // 确保长度为64字符
    while (result.length() < 64) {
        result = "0" + result;
    }
    if (result.length() > 64) {
        result = result.substr(0, 64);
    }
    
    return result;
}

// 生成code_challenge
std::string generate_code_challenge(const std::string& code_verifier) {
    // 使用简化的哈希函数
    const std::string hash_hex = simple_hash(code_verifier);
    
    // 将十六进制转换为二进制
    std::string binary_hash;
    for (size_t i = 0; i < hash_hex.length(); i += 2) {
        const std::string byte_string = hash_hex.substr(i, 2);
        const char byte = static_cast<char>(std::stoi(byte_string, nullptr, 16));
        binary_hash += byte;
    }
    
    // 转换为Base64URL编码
    return base64url_encode_simple(binary_hash);
}

// 生成UUID格式的state
std::string generate_state() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 15);
    
    std::ostringstream oss;
    oss << std::hex;
    
    // 生成UUID格式: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
    for (int i = 0; i < 32; ++i) {
        if (i == 8 || i == 12 || i == 16 || i == 20) {
            oss << '-';
        }
        
        if (i == 12) {
            oss << '4'; // 版本4
        } else if (i == 16) {
            oss << std::hex << (dis(gen) & 0x3 | 0x8); // 变体位
        } else {
            oss << std::hex << dis(gen);
        }
    }
    
    return oss.str();
}

// URL编码
std::string url_encode(const std::string& str) {
    std::ostringstream oss;
    oss << std::hex << std::uppercase << std::setfill('0');
    
    for (unsigned char c : str) {
        if (std::isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
            oss << c;
        } else {
            oss << '%' << std::setw(2) << static_cast<unsigned int>(c);
        }
    }
    
    return oss.str();
}

// 生成OAuth授权URL
std::string generate_oauth_url() {
    // 配置参数
    const std::string auth_base_url = "https://auth.augmentcode.com/authorize";
    const std::string client_id = "augment-vscode-extension";
    const std::string redirect_uri = "vscode://augment.vscode-augment/auth/result";
    const std::string scope = "email";
    const std::string response_type = "code";
    const std::string code_challenge_method = "S256";
    const std::string prompt = "login";
    
    // 生成PKCE参数
    const std::string code_verifier = generate_random_string(128);
    const std::string code_challenge = generate_code_challenge(code_verifier);
    const std::string state = generate_state();
    
    // 构建URL
    std::ostringstream url;
    url << auth_base_url << "?";
    url << "response_type=" << url_encode(response_type) << "&";
    url << "code_challenge=" << url_encode(code_challenge) << "&";
    url << "code_challenge_method=" << url_encode(code_challenge_method) << "&";
    url << "client_id=" << url_encode(client_id) << "&";
    url << "redirect_uri=" << url_encode(redirect_uri) << "&";
    url << "state=" << url_encode(state) << "&";
    url << "scope=" << url_encode(scope) << "&";
    url << "prompt=" << url_encode(prompt);
    
    // 输出调试信息
    std::cout << "=== OAuth PKCE参数生成 ===" << std::endl;
    std::cout << "Code Verifier: " << code_verifier.substr(0, 20) << "..." << std::endl;
    std::cout << "Code Challenge: " << code_challenge << std::endl;
    std::cout << "State: " << state << std::endl;
    std::cout << "Challenge Method: " << code_challenge_method << std::endl;
    std::cout << std::endl;
    
    return url.str();
}

int main() {
    std::cout << "=== Augment OAuth PKCE URL生成器测试 ===" << std::endl;
    std::cout << std::endl;
    
    try {
        // 生成OAuth URL
        const std::string oauth_url = generate_oauth_url();
        
        std::cout << "生成的OAuth授权URL:" << std::endl;
        std::cout << oauth_url << std::endl;
        std::cout << std::endl;
        
        // 验证参数格式
        std::cout << "=== 参数验证 ===" << std::endl;
        
        // 测试多次生成
        std::cout << "生成多个示例进行对比:" << std::endl;
        for (int i = 1; i <= 3; ++i) {
            const std::string code_verifier = generate_random_string(128);
            const std::string code_challenge = generate_code_challenge(code_verifier);
            const std::string state = generate_state();
            
            std::cout << i << ". Code Challenge: " << code_challenge << std::endl;
            std::cout << "   State: " << state << std::endl;
            std::cout << "   Verifier Length: " << code_verifier.length() << std::endl;
            std::cout << std::endl;
        }
        
        std::cout << "✅ OAuth PKCE URL生成测试成功！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
