/**
 * @file config.hpp
 * @brief 项目配置文件 - 由CMake自动生成
 * <AUTHOR> Agent
 * @date 2025-01-05
 */

#pragma once

#include <string>

namespace augment {
namespace config {

// 项目信息
constexpr const char* PROJECT_NAME = "@PROJECT_NAME@";
constexpr const char* PROJECT_VERSION = "@PROJECT_VERSION@";
constexpr const char* PROJECT_DESCRIPTION = "@PROJECT_DESCRIPTION@";

// 构建信息
constexpr const char* BUILD_TYPE = "@CMAKE_BUILD_TYPE@";
constexpr const char* CXX_COMPILER = "@CMAKE_CXX_COMPILER_ID@";
constexpr const char* CXX_COMPILER_VERSION = "@CMAKE_CXX_COMPILER_VERSION@";
constexpr int CXX_STANDARD = @CMAKE_CXX_STANDARD@;

// 依赖库版本
constexpr const char* OPENSSL_VERSION = "@OPENSSL_VERSION@";

// 默认配置值
namespace defaults {
    constexpr const char* SERVER_HOST = "localhost";
    constexpr int SERVER_PORT = 3000;
    constexpr const char* SESSION_STORE_PATH = "./sessions";
    constexpr const char* AUTH_BASE_URL = "https://auth.augmentcode.com/authorize";
    constexpr const char* CLIENT_ID = "augment-vscode-extension";
    constexpr const char* REDIRECT_URI = "vscode://augment.vscode-augment/auth/result";
    constexpr const char* SCOPE = "email";
    constexpr const char* USER_AGENT = "Augment-Auth-Server/1.0";
    
    // 超时设置（秒）
    constexpr int REQUEST_TIMEOUT = 30;
    constexpr int CONNECT_TIMEOUT = 10;
    constexpr int READ_TIMEOUT = 30;
    
    // 重试设置
    constexpr int MAX_RETRIES = 3;
    constexpr int RETRY_DELAY_MS = 1000;
    
    // 会话设置
    constexpr int SESSION_TTL_HOURS = 24 * 30; // 30天
    constexpr int CLEANUP_INTERVAL_MINUTES = 60;
    constexpr int PENDING_AUTH_TIMEOUT_MINUTES = 10;
    constexpr int TOKEN_REFRESH_THRESHOLD_SECONDS = 300; // 5分钟
}

// 功能开关
namespace features {
#ifdef DEBUG
    constexpr bool DEBUG_MODE = true;
#else
    constexpr bool DEBUG_MODE = false;
#endif

#ifdef CPPHTTPLIB_OPENSSL_SUPPORT
    constexpr bool SSL_SUPPORT = true;
#else
    constexpr bool SSL_SUPPORT = false;
#endif

#ifdef CPPHTTPLIB_ZLIB_SUPPORT
    constexpr bool COMPRESSION_SUPPORT = true;
#else
    constexpr bool COMPRESSION_SUPPORT = false;
#endif
}

// 限制和约束
namespace limits {
    constexpr size_t MAX_REQUEST_SIZE = 1024 * 1024; // 1MB
    constexpr size_t MAX_RESPONSE_SIZE = 10 * 1024 * 1024; // 10MB
    constexpr size_t MAX_SESSIONS = 1000;
    constexpr size_t MAX_PENDING_AUTHS = 100;
    constexpr size_t MAX_BACKUPS = 5;
    
    // 字符串长度限制
    constexpr size_t MAX_ACCOUNT_ID_LENGTH = 256;
    constexpr size_t MAX_EMAIL_LENGTH = 320;
    constexpr size_t MAX_URL_LENGTH = 2048;
    constexpr size_t MAX_TOKEN_LENGTH = 4096;
    
    // PKCE限制
    constexpr size_t MIN_CODE_VERIFIER_LENGTH = 43;
    constexpr size_t MAX_CODE_VERIFIER_LENGTH = 128;
    constexpr size_t CODE_CHALLENGE_LENGTH = 43;
}

// 错误码
namespace error_codes {
    constexpr int SUCCESS = 0;
    constexpr int GENERIC_ERROR = 1;
    constexpr int INVALID_ARGUMENTS = 2;
    constexpr int NETWORK_ERROR = 3;
    constexpr int AUTH_ERROR = 4;
    constexpr int STORAGE_ERROR = 5;
    constexpr int CONFIG_ERROR = 6;
    constexpr int TIMEOUT_ERROR = 7;
}

// HTTP状态码
namespace http_status {
    constexpr int OK = 200;
    constexpr int CREATED = 201;
    constexpr int NO_CONTENT = 204;
    constexpr int BAD_REQUEST = 400;
    constexpr int UNAUTHORIZED = 401;
    constexpr int FORBIDDEN = 403;
    constexpr int NOT_FOUND = 404;
    constexpr int METHOD_NOT_ALLOWED = 405;
    constexpr int CONFLICT = 409;
    constexpr int INTERNAL_SERVER_ERROR = 500;
    constexpr int NOT_IMPLEMENTED = 501;
    constexpr int SERVICE_UNAVAILABLE = 503;
}

// 日志级别
enum class LogLevel {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    FATAL = 5
};

// 获取版本字符串
inline std::string get_version_string() {
    return std::string(PROJECT_NAME) + " v" + PROJECT_VERSION;
}

// 获取用户代理字符串
inline std::string get_user_agent() {
    return std::string(defaults::USER_AGENT) + " (" + 
           PROJECT_NAME + "/" + PROJECT_VERSION + "; " +
           CXX_COMPILER + "/" + CXX_COMPILER_VERSION + ")";
}

// 获取构建信息
inline std::string get_build_info() {
    return std::string("Built with ") + CXX_COMPILER + " " + CXX_COMPILER_VERSION +
           " (C++" + std::to_string(CXX_STANDARD) + ", " + BUILD_TYPE + " mode)";
}

} // namespace config
} // namespace augment
