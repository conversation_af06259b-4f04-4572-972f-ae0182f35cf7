/**
 * @file session_store.hpp
 * @brief 会话存储器 - C++17实现
 * <AUTHOR> Agent
 * @date 2025-01-05
 * 
 * 现代C++实现的会话存储器
 * 负责账户信息和token的持久化存储
 */

#pragma once

#include <string>
#include <string_view>
#include <vector>
#include <optional>
#include <chrono>
#include <future>
#include <memory>
#include <filesystem>
#include <mutex>

// 第三方库依赖
#ifdef HAVE_NLOHMANN_JSON
#include <nlohmann/json.hpp>
#else
#include "simple_json.hpp"
#endif

namespace augment {

/**
 * @brief 会话存储异常类
 */
class SessionStoreException : public std::runtime_error {
public:
    explicit SessionStoreException(const std::string& message)
        : std::runtime_error("会话存储错误: " + message) {}
};

/**
 * @brief 会话数据结构
 */
struct SessionData {
    std::string account_id;       ///< 账户ID
    std::string email;            ///< 邮箱地址
    std::string tenant_url;       ///< 租户URL
    std::string access_token;     ///< 访问token
    std::string refresh_token;    ///< 刷新token
    std::string token_type;       ///< Token类型
    std::optional<std::chrono::system_clock::time_point> expires_at; ///< 过期时间
    std::string scope;            ///< 授权范围
    std::chrono::system_clock::time_point created_at;  ///< 创建时间
    std::chrono::system_clock::time_point last_used;   ///< 最后使用时间
    
    /**
     * @brief 检查会话是否已过期
     * @return 如果已过期则返回true
     */
    [[nodiscard]] bool is_expired() const noexcept;
    
    /**
     * @brief 更新最后使用时间
     */
    void update_last_used() noexcept;
    
    /**
     * @brief 转换为JSON
     * @return JSON表示
     */
    [[nodiscard]] nlohmann::json to_json() const;
    
    /**
     * @brief 从JSON创建会话数据
     * @param json JSON数据
     * @return 会话数据
     * @throws SessionStoreException 解析失败时
     */
    [[nodiscard]] static SessionData from_json(const nlohmann::json& json);
};

/**
 * @brief 会话摘要结构（用于列表显示）
 */
struct SessionSummary {
    std::string account_id;
    std::string email;
    std::string tenant_url;
    std::chrono::system_clock::time_point last_used;
    bool is_expired;
    bool is_current;
};

/**
 * @brief 会话存储配置
 */
struct SessionStoreConfig {
    std::filesystem::path storage_path = "./sessions";
    std::string encryption_key;  ///< 可选的加密密钥
    bool encrypt_tokens = true;  ///< 是否加密token
    std::chrono::hours session_ttl{24 * 30}; ///< 会话TTL (30天)
    bool auto_cleanup = true;    ///< 自动清理过期会话
    std::chrono::minutes cleanup_interval{60}; ///< 清理间隔
    
    // 文件系统配置
    struct FileSystemConfig {
        std::filesystem::perms file_permissions{std::filesystem::perms::owner_read | 
                                               std::filesystem::perms::owner_write};
        std::filesystem::perms dir_permissions{std::filesystem::perms::owner_all};
        bool create_backup = true;
        std::size_t max_backups = 5;
    } filesystem;
};

/**
 * @brief 会话存储器类
 * 
 * 负责账户会话的持久化存储和管理
 * 支持加密存储、自动清理、备份等功能
 */
class SessionStore {
public:
    /**
     * @brief 构造函数
     * @param config 会话存储配置
     */
    explicit SessionStore(const SessionStoreConfig& config = {});
    
    /**
     * @brief 析构函数
     */
    ~SessionStore();
    
    // 禁用拷贝，允许移动
    SessionStore(const SessionStore&) = delete;
    SessionStore& operator=(const SessionStore&) = delete;
    SessionStore(SessionStore&&) = default;
    SessionStore& operator=(SessionStore&&) = default;
    
    /**
     * @brief 初始化会话存储
     * @return 初始化完成的Future
     * @throws SessionStoreException 初始化失败时
     */
    [[nodiscard]] std::future<void> initialize();
    
    /**
     * @brief 存储会话数据
     * @param session 会话数据
     * @return 存储完成的Future
     * @throws SessionStoreException 存储失败时
     */
    [[nodiscard]] std::future<void> store_session(const SessionData& session);
    
    /**
     * @brief 获取会话数据
     * @param account_id 账户ID
     * @return 会话数据或nullopt
     */
    [[nodiscard]] std::future<std::optional<SessionData>> get_session(const std::string& account_id);
    
    /**
     * @brief 更新会话数据
     * @param account_id 账户ID
     * @param updates 更新的字段
     * @return 更新完成的Future
     * @throws SessionStoreException 更新失败时
     */
    [[nodiscard]] std::future<void> update_session(const std::string& account_id, 
                                                   const nlohmann::json& updates);
    
    /**
     * @brief 移除会话
     * @param account_id 账户ID
     * @return 移除完成的Future
     */
    [[nodiscard]] std::future<void> remove_session(const std::string& account_id);
    
    /**
     * @brief 列出所有会话
     * @return 会话摘要列表
     */
    [[nodiscard]] std::future<std::vector<SessionSummary>> list_sessions();
    
    /**
     * @brief 设置当前账户
     * @param account_id 账户ID（空字符串表示清除）
     * @return 设置完成的Future
     */
    [[nodiscard]] std::future<void> set_current_account(const std::string& account_id);
    
    /**
     * @brief 获取当前账户ID
     * @return 当前账户ID或nullopt
     */
    [[nodiscard]] std::future<std::optional<std::string>> get_current_account();
    
    /**
     * @brief 清理过期会话
     * @return 清理的会话数量
     */
    [[nodiscard]] std::future<std::size_t> cleanup_expired_sessions();
    
    /**
     * @brief 创建会话备份
     * @return 备份文件路径
     * @throws SessionStoreException 备份失败时
     */
    [[nodiscard]] std::future<std::filesystem::path> create_backup();
    
    /**
     * @brief 从备份恢复会话
     * @param backup_path 备份文件路径
     * @return 恢复完成的Future
     * @throws SessionStoreException 恢复失败时
     */
    [[nodiscard]] std::future<void> restore_from_backup(const std::filesystem::path& backup_path);
    
    /**
     * @brief 获取存储统计信息
     * @return 统计信息JSON
     */
    [[nodiscard]] std::future<nlohmann::json> get_storage_stats();
    
    /**
     * @brief 验证存储完整性
     * @return 验证结果
     */
    [[nodiscard]] std::future<bool> validate_storage_integrity();

private:
    /**
     * @brief 确保存储目录存在
     */
    void ensure_storage_directory();
    
    /**
     * @brief 获取会话文件路径
     * @param account_id 账户ID
     * @return 文件路径
     */
    [[nodiscard]] std::filesystem::path get_session_file_path(const std::string& account_id) const;
    
    /**
     * @brief 获取当前账户文件路径
     * @return 文件路径
     */
    [[nodiscard]] std::filesystem::path get_current_account_file_path() const;
    
    /**
     * @brief 加密数据
     * @param data 原始数据
     * @return 加密后的数据
     */
    [[nodiscard]] std::string encrypt_data(const std::string& data) const;
    
    /**
     * @brief 解密数据
     * @param encrypted_data 加密的数据
     * @return 解密后的数据
     * @throws SessionStoreException 解密失败时
     */
    [[nodiscard]] std::string decrypt_data(const std::string& encrypted_data) const;
    
    /**
     * @brief 读取文件内容
     * @param file_path 文件路径
     * @return 文件内容
     * @throws SessionStoreException 读取失败时
     */
    [[nodiscard]] std::string read_file(const std::filesystem::path& file_path) const;
    
    /**
     * @brief 写入文件内容
     * @param file_path 文件路径
     * @param content 文件内容
     * @throws SessionStoreException 写入失败时
     */
    void write_file(const std::filesystem::path& file_path, const std::string& content) const;
    
    /**
     * @brief 安全删除文件
     * @param file_path 文件路径
     */
    void secure_delete_file(const std::filesystem::path& file_path) const;
    
    /**
     * @brief 启动自动清理任务
     */
    void start_auto_cleanup();
    
    /**
     * @brief 停止自动清理任务
     */
    void stop_auto_cleanup();
    
    /**
     * @brief 生成加密密钥
     * @return 加密密钥
     */
    [[nodiscard]] std::string generate_encryption_key() const;
    
    /**
     * @brief 验证账户ID格式
     * @param account_id 账户ID
     * @return 如果格式有效则返回true
     */
    [[nodiscard]] static bool is_valid_account_id(const std::string& account_id);

private:
    SessionStoreConfig config_;
    std::filesystem::path storage_path_;
    std::string encryption_key_;
    mutable std::mutex storage_mutex_;
    
    // 自动清理任务
    std::atomic<bool> cleanup_running_{false};
    std::unique_ptr<std::thread> cleanup_thread_;
    
    // 初始化状态
    std::atomic<bool> initialized_{false};
};

/**
 * @brief 会话存储构建器类
 * 
 * 提供流式API来配置和创建SessionStore实例
 */
class SessionStoreBuilder {
public:
    SessionStoreBuilder() = default;
    
    SessionStoreBuilder& storage_path(const std::filesystem::path& path) {
        config_.storage_path = path;
        return *this;
    }
    
    SessionStoreBuilder& encryption_key(const std::string& key) {
        config_.encryption_key = key;
        return *this;
    }
    
    SessionStoreBuilder& encrypt_tokens(bool encrypt = true) {
        config_.encrypt_tokens = encrypt;
        return *this;
    }
    
    SessionStoreBuilder& session_ttl(std::chrono::hours ttl) {
        config_.session_ttl = ttl;
        return *this;
    }
    
    SessionStoreBuilder& auto_cleanup(bool enable = true) {
        config_.auto_cleanup = enable;
        return *this;
    }
    
    SessionStoreBuilder& cleanup_interval(std::chrono::minutes interval) {
        config_.cleanup_interval = interval;
        return *this;
    }
    
    SessionStoreBuilder& create_backup(bool enable = true) {
        config_.filesystem.create_backup = enable;
        return *this;
    }
    
    [[nodiscard]] std::unique_ptr<SessionStore> build() {
        return std::make_unique<SessionStore>(config_);
    }

private:
    SessionStoreConfig config_;
};

} // namespace augment
