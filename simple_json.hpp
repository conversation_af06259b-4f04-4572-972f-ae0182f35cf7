/**
 * @file simple_json.hpp
 * @brief Simplified JSON implementation for testing only
 * <AUTHOR> Agent
 * @date 2025-01-05
 *
 * This is a minimal JSON implementation for use when n<PERSON>hmann/json is not available
 * Not recommended for production use
 */

#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <sstream>
#include <stdexcept>
#include <type_traits>
#include <initializer_list>

namespace simple_json {

class json {
public:
    enum class value_type {
        null,
        object,
        array,
        string,
        number,
        boolean
    };

private:
    value_type type_ = value_type::null;
    std::string string_value_;
    double number_value_ = 0.0;
    bool bool_value_ = false;
    std::unordered_map<std::string, json> object_value_;
    std::vector<json> array_value_;

public:
    // Constructors
    json() = default;
    json(const std::string& value) : type_(value_type::string), string_value_(value) {}
    json(const char* value) : type_(value_type::string), string_value_(value) {}
    json(double value) : type_(value_type::number), number_value_(value) {}
    json(int value) : type_(value_type::number), number_value_(static_cast<double>(value)) {}
    json(long long value) : type_(value_type::number), number_value_(static_cast<double>(value)) {}
    json(bool value) : type_(value_type::boolean), bool_value_(value) {}

    // Initializer list constructor for objects
    json(std::initializer_list<std::pair<std::string, json>> init) : type_(value_type::object) {
        for (const auto& pair : init) {
            object_value_[pair.first] = pair.second;
        }
    }

    // Initializer list constructor for arrays
    json(std::initializer_list<json> init) : type_(value_type::array) {
        for (const auto& item : init) {
            array_value_.push_back(item);
        }
    }

    // Type checking
    bool is_null() const { return type_ == value_type::null; }
    bool is_object() const { return type_ == value_type::object; }
    bool is_array() const { return type_ == value_type::array; }
    bool is_string() const { return type_ == value_type::string; }
    bool is_number() const { return type_ == value_type::number; }
    bool is_boolean() const { return type_ == value_type::boolean; }

    // Value access
    std::string get_string() const {
        if (type_ != value_type::string) throw std::runtime_error("Not a string type");
        return string_value_;
    }

    double get_number() const {
        if (type_ != value_type::number) throw std::runtime_error("Not a number type");
        return number_value_;
    }

    bool get_boolean() const {
        if (type_ != value_type::boolean) throw std::runtime_error("Not a boolean type");
        return bool_value_;
    }

    // Conversion operators for compatibility
    operator std::string() const {
        if (type_ == value_type::string) return string_value_;
        throw std::runtime_error("Cannot convert to string");
    }

    operator int() const {
        if (type_ == value_type::number) return static_cast<int>(number_value_);
        throw std::runtime_error("Cannot convert to int");
    }

    operator double() const {
        if (type_ == value_type::number) return number_value_;
        throw std::runtime_error("Cannot convert to double");
    }

    // Additional methods for nlohmann/json compatibility
    std::string value(const std::string& key, const std::string& default_value) const {
        if (type_ == value_type::object && contains(key)) {
            const auto& val = (*this)[key];
            if (val.is_string()) return val.get_string();
        }
        return default_value;
    }

    // Static factory methods
    static json array() {
        json result;
        result.type_ = value_type::array;
        return result;
    }

    static json object() {
        json result;
        result.type_ = value_type::object;
        return result;
    }

    // Assignment operators
    json& operator=(const std::string& value) {
        type_ = value_type::string;
        string_value_ = value;
        return *this;
    }

    json& operator=(int value) {
        type_ = value_type::number;
        number_value_ = static_cast<double>(value);
        return *this;
    }

    json& operator=(long long value) {
        type_ = value_type::number;
        number_value_ = static_cast<double>(value);
        return *this;
    }

    json& operator=(double value) {
        type_ = value_type::number;
        number_value_ = value;
        return *this;
    }

    json& operator=(bool value) {
        type_ = value_type::boolean;
        bool_value_ = value;
        return *this;
    }

    // Object operations
    json& operator[](const std::string& key) {
        if (type_ == value_type::null) {
            type_ = value_type::object;
        }
        if (type_ != value_type::object) throw std::runtime_error("Not an object type");
        return object_value_[key];
    }

    const json& operator[](const std::string& key) const {
        if (type_ != value_type::object) throw std::runtime_error("Not an object type");
        auto it = object_value_.find(key);
        if (it == object_value_.end()) throw std::runtime_error("Key not found");
        return it->second;
    }

    bool contains(const std::string& key) const {
        if (type_ != value_type::object) return false;
        return object_value_.find(key) != object_value_.end();
    }

    // Array operations
    void push_back(const json& value) {
        if (type_ == value_type::null) {
            type_ = value_type::array;
        }
        if (type_ != value_type::array) throw std::runtime_error("Not an array type");
        array_value_.push_back(value);
    }

    size_t size() const {
        if (type_ == value_type::array) return array_value_.size();
        if (type_ == value_type::object) return object_value_.size();
        return 0;
    }

    // Serialization
    std::string dump() const {
        std::ostringstream oss;
        dump_to_stream(oss);
        return oss.str();
    }

    std::string dump(int indent) const {
        // For simplicity, ignore indent parameter
        (void)indent;
        return dump();
    }

    // Template get method for compatibility
    template<typename T>
    T get() const {
        if constexpr (std::is_same_v<T, std::string>) {
            return get_string();
        } else if constexpr (std::is_same_v<T, int>) {
            return static_cast<int>(get_number());
        } else if constexpr (std::is_same_v<T, double>) {
            return get_number();
        } else if constexpr (std::is_same_v<T, bool>) {
            return get_boolean();
        } else {
            throw std::runtime_error("Unsupported type for get<T>()");
        }
    }

    // Static parse method
    static json parse(const std::string& str) {
        // Simple parsing - just return a string value for now
        return json(str);
    }

private:
    void dump_to_stream(std::ostringstream& oss) const {
        switch (type_) {
            case value_type::null:
                oss << "null";
                break;
            case value_type::string:
                oss << "\"" << escape_string(string_value_) << "\"";
                break;
            case value_type::number:
                oss << number_value_;
                break;
            case value_type::boolean:
                oss << (bool_value_ ? "true" : "false");
                break;
            case value_type::object:
                {
                    oss << "{";
                    bool first = true;
                    for (const auto& pair : object_value_) {
                        if (!first) oss << ",";
                        oss << "\"" << escape_string(pair.first) << "\":";
                        pair.second.dump_to_stream(oss);
                        first = false;
                    }
                    oss << "}";
                }
                break;
            case value_type::array:
                oss << "[";
                for (size_t i = 0; i < array_value_.size(); ++i) {
                    if (i > 0) oss << ",";
                    array_value_[i].dump_to_stream(oss);
                }
                oss << "]";
                break;
        }
    }

    std::string escape_string(const std::string& str) const {
        std::string result;
        for (char c : str) {
            switch (c) {
                case '"': result += "\\\""; break;
                case '\\': result += "\\\\"; break;
                case '\n': result += "\\n"; break;
                case '\r': result += "\\r"; break;
                case '\t': result += "\\t"; break;
                default: result += c; break;
            }
        }
        return result;
    }
};

// Simple JSON parsing (basic format only)
inline json parse(const std::string& str) {
    // This is a placeholder implementation
    // In real use, you need a complete JSON parser
    json result;
    if (str == "null") {
        return result;
    } else if (str == "true") {
        return json(true);
    } else if (str == "false") {
        return json(false);
    } else if (str.front() == '"' && str.back() == '"') {
        return json(str.substr(1, str.length() - 2));
    } else {
        try {
            double num = std::stod(str);
            return json(num);
        } catch (...) {
            throw std::runtime_error("Cannot parse JSON: " + str);
        }
    }
}

} // namespace simple_json

// Compatibility alias
#ifndef HAVE_NLOHMANN_JSON
namespace nlohmann {
    using json = simple_json::json;

    // Global parse function
    inline simple_json::json parse(const std::string& str) {
        return simple_json::parse(str);
    }

    // Exception class for compatibility
    class exception : public std::runtime_error {
    public:
        exception(const std::string& msg) : std::runtime_error(msg) {}
    };
}

// Global parse function for compatibility
inline simple_json::json parse(const std::string& str) {
    return simple_json::parse(str);
}
#endif
