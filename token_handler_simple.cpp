/**
 * @file token_handler_simple.cpp
 * @brief 简化的Token处理程序 - 处理OAuth回调和token提取
 */

#include <iostream>
#include <string>
#include <sstream>
#include <unordered_map>
#include <algorithm>

// URL解码
std::string url_decode(const std::string& str) {
    std::string result;
    result.reserve(str.length());
    
    for (std::size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '%' && i + 2 < str.length()) {
            const std::string hex = str.substr(i + 1, 2);
            const auto value = static_cast<char>(std::stoi(hex, nullptr, 16));
            result += value;
            i += 2;
        } else if (str[i] == '+') {
            result += ' ';
        } else {
            result += str[i];
        }
    }
    
    return result;
}

// 解析查询字符串
std::unordered_map<std::string, std::string> parse_query_string(const std::string& query_string) {
    std::unordered_map<std::string, std::string> params;
    
    if (query_string.empty()) {
        return params;
    }
    
    std::istringstream iss(query_string);
    std::string pair;
    
    while (std::getline(iss, pair, '&')) {
        const auto eq_pos = pair.find('=');
        if (eq_pos != std::string::npos) {
            const std::string key = url_decode(pair.substr(0, eq_pos));
            const std::string value = url_decode(pair.substr(eq_pos + 1));
            params[key] = value;
        }
    }
    
    return params;
}

// 简单的JSON解析（手动解析特定格式）
std::unordered_map<std::string, std::string> parse_simple_json(const std::string& json) {
    std::unordered_map<std::string, std::string> result;
    
    // 移除花括号和空格
    std::string content = json;
    content.erase(std::remove_if(content.begin(), content.end(), 
                                [](char c) { return c == '{' || c == '}' || c == ' '; }), 
                 content.end());
    
    // 按逗号分割
    std::istringstream iss(content);
    std::string pair;
    
    while (std::getline(iss, pair, ',')) {
        // 查找冒号
        auto colon_pos = pair.find(':');
        if (colon_pos != std::string::npos) {
            std::string key = pair.substr(0, colon_pos);
            std::string value = pair.substr(colon_pos + 1);
            
            // 移除引号
            if (key.front() == '"' && key.back() == '"') {
                key = key.substr(1, key.length() - 2);
            }
            if (value.front() == '"' && value.back() == '"') {
                value = value.substr(1, value.length() - 2);
            }
            
            result[key] = value;
        }
    }
    
    return result;
}

// 解析OAuth回调URL
void parse_oauth_callback(const std::string& callback_url) {
    std::cout << "=== Parse OAuth Callback URL ===" << std::endl;
    std::cout << "Callback URL: " << callback_url << std::endl;
    std::cout << std::endl;
    
    // 查找查询字符串部分
    auto query_pos = callback_url.find('?');
    if (query_pos == std::string::npos) {
        std::cout << "ERROR: No query string found" << std::endl;
        return;
    }
    
    const std::string query_string = callback_url.substr(query_pos + 1);
    const auto params = parse_query_string(query_string);
    
    std::cout << "Extracted parameters:" << std::endl;
    for (const auto& [key, value] : params) {
        std::cout << "  " << key << ": " << value << std::endl;
    }
    std::cout << std::endl;
    
    // 检查必需参数
    auto code_it = params.find("code");
    auto state_it = params.find("state");
    auto tenant_url_it = params.find("tenant_url");
    
    if (code_it != params.end()) {
        std::cout << "SUCCESS: Found authorization code: " << code_it->second << std::endl;
    } else {
        std::cout << "ERROR: Missing authorization code" << std::endl;
    }
    
    if (state_it != params.end()) {
        std::cout << "SUCCESS: Found state parameter: " << state_it->second << std::endl;
    } else {
        std::cout << "ERROR: Missing state parameter" << std::endl;
    }
    
    if (tenant_url_it != params.end()) {
        std::cout << "SUCCESS: Found tenant URL: " << tenant_url_it->second << std::endl;
    } else {
        std::cout << "WARNING: Tenant URL not found" << std::endl;
    }
}

// 处理token提取JSON
void handle_token_extraction(const std::string& json_response) {
    std::cout << "=== Handle Token Extraction Response ===" << std::endl;
    std::cout << "JSON Response: " << json_response << std::endl;
    std::cout << std::endl;
    
    try {
        const auto data = parse_simple_json(json_response);
        
        std::cout << "Parsed data:" << std::endl;
        for (const auto& [key, value] : data) {
            std::cout << "  " << key << ": " << value << std::endl;
        }
        std::cout << std::endl;
        
        // 检查必需字段
        auto code_it = data.find("code");
        auto state_it = data.find("state");
        auto tenant_url_it = data.find("tenant_url");
        
        if (code_it != data.end()) {
            std::cout << "SUCCESS: Authorization code: " << code_it->second << std::endl;
        }
        
        if (state_it != data.end()) {
            std::cout << "SUCCESS: State: " << state_it->second << std::endl;
        }
        
        if (tenant_url_it != data.end()) {
            std::cout << "SUCCESS: Tenant URL: " << tenant_url_it->second << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "ERROR: JSON parsing failed: " << e.what() << std::endl;
    }
}

// 模拟token交换（GET请求）
void simulate_token_exchange(const std::string& code, const std::string& tenant_url, 
                           const std::string& code_verifier) {
    std::cout << "=== Simulate Token Exchange (GET Request) ===" << std::endl;
    
    // 构建token交换URL（使用GET请求）
    std::ostringstream token_url;
    token_url << tenant_url;
    if (tenant_url.back() != '/') token_url << '/';
    token_url << "oauth/token?";
    token_url << "grant_type=authorization_code&";
    token_url << "code=" << code << "&";
    token_url << "code_verifier=" << code_verifier << "&";
    token_url << "client_id=v&";
    token_url << "redirect_uri=vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult";
    
    std::cout << "Token Exchange URL (GET):" << std::endl;
    std::cout << token_url.str() << std::endl;
    std::cout << std::endl;
    
    // 模拟响应
    const std::string mock_token = "3088021e04396f9bef344c866d4f7fdf417d545d3ff2417399e9b166ca7c4941";
    std::cout << "Mock Token Response: " << mock_token << std::endl;
    std::cout << "Token Length: " << mock_token.length() << " characters" << std::endl;
    
    // 验证token格式（应该是64字符的十六进制字符串）
    if (mock_token.length() == 64) {
        bool is_hex = std::all_of(mock_token.begin(), mock_token.end(), 
                                 [](char c) { return std::isxdigit(c); });
        if (is_hex) {
            std::cout << "SUCCESS: Token format is valid (64-character hex)" << std::endl;
        } else {
            std::cout << "ERROR: Token format is invalid (contains non-hex characters)" << std::endl;
        }
    } else {
        std::cout << "ERROR: Token length is invalid (should be 64 characters)" << std::endl;
    }
}

int main() {
    std::cout << "=== Augment OAuth Token Handler Test ===" << std::endl;
    std::cout << std::endl;
    
    // Example 1: Parse OAuth callback URL
    const std::string callback_url = 
        "vscode://augment.vscode-augment/auth/result?code=_374dc88ef229ce029e5e997430f0d2c8&state=855K7No7Mro&tenant_url=https%3A%2F%2Fd10.api.augmentcode.com%2F";
    
    parse_oauth_callback(callback_url);
    std::cout << std::string(50, '-') << std::endl;
    
    // Example 2: Handle token extraction JSON
    const std::string json_response = 
        R"({"code":"_374dc88ef229ce029e5e997430f0d2c8","state":"855K7No7Mro","tenant_url":"https://d10.api.augmentcode.com/"})";
    
    handle_token_extraction(json_response);
    std::cout << std::string(50, '-') << std::endl;
    
    // Example 3: Simulate token exchange
    const std::string code = "_374dc88ef229ce029e5e997430f0d2c8";
    const std::string tenant_url = "https://d10.api.augmentcode.com/";
    const std::string code_verifier = "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk"; // Example
    
    simulate_token_exchange(code, tenant_url, code_verifier);
    
    std::cout << std::endl;
    std::cout << "SUCCESS: Token handler test completed!" << std::endl;
    
    return 0;
}
