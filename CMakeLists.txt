# Augment认证Web服务器 - CMake配置文件
# 最低CMake版本要求
cmake_minimum_required(VERSION 3.16)

# 项目信息
project(AugmentAuthWebServer
    VERSION 1.0.0
    DESCRIPTION "Augment认证Web服务器 - 现代C++实现"
    LANGUAGES CXX
)

# C++标准设置
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译器标志
if(MSVC)
    # MSVC编译器设置
    add_compile_options(/W4 /permissive- /utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    add_compile_definitions(WIN32_LEAN_AND_MEAN)
    add_compile_definitions(NOMINMAX)
    # 禁用特定警告
    add_compile_options(/wd4819)  # 禁用字符编码警告
else()
    # GCC/Clang编译器设置
    add_compile_options(
        -Wall -Wextra -Wpedantic
        -Wconversion -Wsign-conversion
        -Wold-style-cast -Wcast-align
        -Wunused -Woverloaded-virtual
        -Wnon-virtual-dtor -Wcast-qual
        -Wshadow -Wnull-dereference
    )
endif()

# 构建类型设置
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Debug和Release特定设置
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG -fsanitize=address -fsanitize=undefined")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -march=native")

# 查找依赖库
# find_package(PkgConfig REQUIRED)  # Windows下可能不需要

# 查找OpenSSL（可选）
find_package(OpenSSL QUIET)
if(OpenSSL_FOUND)
    message(STATUS "找到OpenSSL版本: ${OPENSSL_VERSION}")
    set(HAVE_OPENSSL TRUE)
else()
    message(WARNING "未找到OpenSSL库，将使用替代实现")
    set(HAVE_OPENSSL FALSE)
endif()

# 查找Threads
find_package(Threads REQUIRED)

# 查找nlohmann/json（可选）
find_package(nlohmann_json QUIET)
if(nlohmann_json_FOUND)
    message(STATUS "找到nlohmann/json")
    set(HAVE_NLOHMANN_JSON TRUE)
else()
    message(WARNING "未找到nlohmann/json，将使用简化的JSON实现")
    set(HAVE_NLOHMANN_JSON FALSE)
endif()

# 查找httplib（可选）
find_path(HTTPLIB_INCLUDE_DIR httplib.h
    PATHS
        /usr/include
        /usr/local/include
        /opt/homebrew/include
        ${CMAKE_SOURCE_DIR}/third_party/httplib
)

if(HTTPLIB_INCLUDE_DIR)
    message(STATUS "找到httplib: ${HTTPLIB_INCLUDE_DIR}")
    set(HAVE_HTTPLIB TRUE)
else()
    message(WARNING "未找到httplib，将使用简化的HTTP实现")
    set(HAVE_HTTPLIB FALSE)
    set(HTTPLIB_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR})
endif()

# 源文件列表
set(SOURCES
    oauth_pkce_generator.cpp
    token_exchanger.cpp
    session_store.cpp
    augment_auth_manager.cpp
    auth_web_server.cpp
    http_client.cpp
    main.cpp
)

# 头文件列表
set(HEADERS
    oauth_pkce_generator.hpp
    token_exchanger.hpp
    session_store.hpp
    augment_auth_manager.hpp
    auth_web_server.hpp
    http_client.hpp
)

# 创建可执行文件
add_executable(augment_auth_server ${SOURCES} ${HEADERS})

# 设置包含目录
target_include_directories(augment_auth_server
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${HTTPLIB_INCLUDE_DIR}
)

# 链接依赖库
target_link_libraries(augment_auth_server
    PRIVATE
        Threads::Threads
)

# 条件链接nlohmann/json
if(HAVE_NLOHMANN_JSON)
    target_link_libraries(augment_auth_server
        PRIVATE
            nlohmann_json::nlohmann_json
    )
endif()

# 条件链接OpenSSL
if(HAVE_OPENSSL)
    target_link_libraries(augment_auth_server
        PRIVATE
            OpenSSL::SSL
            OpenSSL::Crypto
    )
endif()

# 平台特定链接库
if(WIN32)
    target_link_libraries(augment_auth_server PRIVATE ws2_32 wsock32)
endif()

# 设置编译特性
target_compile_features(augment_auth_server
    PRIVATE
        cxx_std_17
)

# 编译定义
target_compile_definitions(augment_auth_server
    PRIVATE
        CPPHTTPLIB_ZLIB_SUPPORT
)

# 条件编译定义
if(HAVE_OPENSSL)
    target_compile_definitions(augment_auth_server
        PRIVATE
            CPPHTTPLIB_OPENSSL_SUPPORT
            HAVE_OPENSSL
    )
endif()

if(HAVE_NLOHMANN_JSON)
    target_compile_definitions(augment_auth_server
        PRIVATE
            HAVE_NLOHMANN_JSON
    )
endif()

# 安装规则
install(TARGETS augment_auth_server
    RUNTIME DESTINATION bin
)

install(FILES ${HEADERS}
    DESTINATION include/augment
)

# 创建配置文件
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/config.hpp.in
    ${CMAKE_CURRENT_BINARY_DIR}/config.hpp
    @ONLY
)

target_include_directories(augment_auth_server
    PRIVATE
        ${CMAKE_CURRENT_BINARY_DIR}
)

# 测试支持
enable_testing()

# 创建单元测试（可选）
option(BUILD_TESTS "构建单元测试" OFF)
if(BUILD_TESTS)
    find_package(GTest QUIET)
    if(GTest_FOUND)
        add_subdirectory(tests)
    else()
        message(WARNING "未找到Google Test，跳过测试构建")
    endif()
endif()

# 性能测试（可选）
option(BUILD_BENCHMARKS "构建性能测试" OFF)
if(BUILD_BENCHMARKS)
    find_package(benchmark QUIET)
    if(benchmark_FOUND)
        add_subdirectory(benchmarks)
    else()
        message(WARNING "未找到Google Benchmark，跳过性能测试构建")
    endif()
endif()

# 代码覆盖率支持（Debug模式）
if(CMAKE_BUILD_TYPE STREQUAL "Debug" AND CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    option(ENABLE_COVERAGE "启用代码覆盖率分析" OFF)
    
    if(ENABLE_COVERAGE)
        target_compile_options(augment_auth_server
            PRIVATE
                --coverage
        )
        
        target_link_options(augment_auth_server
            PRIVATE
                --coverage
        )
        
        message(STATUS "已启用代码覆盖率分析")
    endif()
endif()

# 静态分析支持
option(ENABLE_CLANG_TIDY "启用clang-tidy静态分析" OFF)
if(ENABLE_CLANG_TIDY)
    find_program(CLANG_TIDY_EXE NAMES "clang-tidy")
    if(CLANG_TIDY_EXE)
        set_target_properties(augment_auth_server PROPERTIES
            CXX_CLANG_TIDY "${CLANG_TIDY_EXE};-checks=-*,readability-*,performance-*,modernize-*,bugprone-*"
        )
        message(STATUS "已启用clang-tidy静态分析")
    else()
        message(WARNING "未找到clang-tidy，跳过静态分析")
    endif()
endif()

# AddressSanitizer支持（Debug模式）
option(ENABLE_ASAN "启用AddressSanitizer" OFF)
if(ENABLE_ASAN AND CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(augment_auth_server
        PRIVATE
            -fsanitize=address -fno-omit-frame-pointer
    )
    
    target_link_options(augment_auth_server
        PRIVATE
            -fsanitize=address
    )
    
    message(STATUS "已启用AddressSanitizer")
endif()

# ThreadSanitizer支持（Debug模式）
option(ENABLE_TSAN "启用ThreadSanitizer" OFF)
if(ENABLE_TSAN AND CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(augment_auth_server
        PRIVATE
            -fsanitize=thread
    )
    
    target_link_options(augment_auth_server
        PRIVATE
            -fsanitize=thread
    )
    
    message(STATUS "已启用ThreadSanitizer")
endif()

# 打印构建信息
message(STATUS "=== 构建配置信息 ===")
message(STATUS "项目名称: ${PROJECT_NAME}")
message(STATUS "项目版本: ${PROJECT_VERSION}")
message(STATUS "C++标准: ${CMAKE_CXX_STANDARD}")
message(STATUS "构建类型: ${CMAKE_BUILD_TYPE}")
message(STATUS "编译器: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "OpenSSL版本: ${OPENSSL_VERSION}")
message(STATUS "httplib路径: ${HTTPLIB_INCLUDE_DIR}")

# 使用说明
message(STATUS "")
message(STATUS "=== 构建说明 ===")
message(STATUS "基本构建:")
message(STATUS "  mkdir build && cd build")
message(STATUS "  cmake ..")
message(STATUS "  make -j$(nproc)")
message(STATUS "")
message(STATUS "启用所有功能的构建:")
message(STATUS "  cmake -DCMAKE_BUILD_TYPE=Debug -DENABLE_COVERAGE=ON -DENABLE_CLANG_TIDY=ON -DENABLE_ASAN=ON -DBUILD_TESTS=ON ..")
message(STATUS "")
message(STATUS "运行服务器:")
message(STATUS "  ./augment_auth_server --help")
message(STATUS "  ./augment_auth_server --port 8080 --debug")
message(STATUS "")
message(STATUS "安装:")
message(STATUS "  make install")
