=== Augment OAuth PKCE URL生成器测试 ===

=== OAuth PKCE参数生成 ===
Code Verifier: S79YGFFddKtw_eBIWPtE...
Code Challenge: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAo4RPNFZ-8NE
State: 99f16177-1251-44a7-8733-d26854da5b5b
Challenge Method: S256

生成的OAuth授权URL:
https://auth.augmentcode.com/authorize?response_type=code&code_challenge=AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAo4RPNFZ-8NE&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=99f16177-1251-44a7-8733-d26854da5b5b&scope=email&prompt=login

=== 参数验证 ===
生成多个示例进行对比:
1. Code Challenge: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAbRri66XxrLk
   State: f5bfef89-c291-43ff-9678-c6d238a535a1
   Verifier Length: 128

2. Code Challenge: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyzC7nLyrVXY
   State: 9b3d6d14-d840-45ea-bfaa-f49ee2577416
   Verifier Length: 128

3. Code Challenge: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFUMICnhuy0c
   State: cf9dc075-2685-41e3-8a67-7e95820f006f
   Verifier Length: 128

✅ OAuth PKCE URL生成测试成功！
