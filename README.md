# Augment认证Web服务器 - C++实现

## 概述

这是Augment认证Web服务器的现代C++17实现，完全移植自原始的JavaScript版本。提供OAuth 2.0 + PKCE认证流程的Web界面和RESTful API，支持多账户管理、token刷新、会话存储等功能。

## 功能特性

### 🔐 核心认证功能
- **OAuth 2.0 + PKCE流程**: 符合RFC 7636标准的安全认证
- **多账户管理**: 支持多个Augment账户的存储和切换
- **自动Token刷新**: 智能检测和刷新过期的访问token
- **会话持久化**: 安全的本地会话存储和管理

### 🌐 Web界面和API
- **现代Web界面**: 响应式HTML界面，支持所有认证操作
- **RESTful API**: 完整的HTTP API端点
- **CORS支持**: 跨域资源共享支持
- **实时状态更新**: 动态显示认证状态和账户信息

### 🛡️ 安全特性
- **加密存储**: 可选的token加密存储
- **CSRF保护**: State参数验证防止跨站请求伪造
- **安全随机数**: 使用OpenSSL生成加密安全的随机数
- **输入验证**: 完整的参数格式验证

### ⚡ 性能和可靠性
- **异步处理**: 基于std::future的异步操作
- **连接池**: HTTP客户端连接复用
- **自动重试**: 网络请求失败自动重试
- **内存安全**: RAII和智能指针确保内存安全

## 系统要求

### 编译要求
- **C++编译器**: GCC 7+, Clang 6+, MSVC 2017+
- **C++标准**: C++17或更高版本
- **CMake**: 3.16或更高版本

### 依赖库
- **OpenSSL**: 加密和HTTPS支持 (`libssl-dev`)
- **nlohmann/json**: JSON处理库
- **httplib**: HTTP服务器库
- **Threads**: POSIX线程库

### 运行环境
- **操作系统**: Linux, macOS, Windows
- **内存**: 最少64MB可用内存
- **磁盘**: 最少10MB可用空间（用于会话存储）
- **网络**: 访问auth.augmentcode.com的网络连接

## 快速开始

### 1. 克隆和构建

**Linux/macOS:**
```bash
# 进入cpp目录
cd cpp

# 使用构建脚本（推荐）
chmod +x build.sh
./build.sh --debug --tests

# 或手动构建
mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Debug -DBUILD_TESTS=ON ..
make -j$(nproc)
```

**Windows:**
```cmd
# 进入cpp目录
cd cpp

# 使用构建脚本（推荐）
build.bat --debug --tests

# 或手动构建
mkdir build && cd build
cmake -G "Visual Studio 17 2022" -DCMAKE_BUILD_TYPE=Debug -DBUILD_TESTS=ON ..
cmake --build . --config Debug
```

### 2. 运行服务器

```bash
# 基本运行
./augment_auth_server

# 自定义端口和调试模式
./augment_auth_server --port 8080 --debug

# 查看所有选项
./augment_auth_server --help
```

### 3. 访问Web界面

打开浏览器访问：`http://localhost:3000`

## 构建选项

### 构建脚本参数

**Linux/macOS (build.sh):**
```bash
./build.sh [选项]

选项:
  -d, --debug         Debug构建模式
  -r, --release       Release构建模式 (默认)
  -t, --tests         启用单元测试
  -b, --benchmarks    启用性能测试
  -c, --coverage      启用代码覆盖率
  --asan              启用AddressSanitizer
  --tsan              启用ThreadSanitizer
  --clang-tidy        启用clang-tidy静态分析
  --clean             清理构建目录
  --install-deps      尝试安装依赖库
  -v, --verbose       详细输出
```

**Windows (build.bat):**
```cmd
build.bat [选项]

选项:
  -d, --debug         Debug构建模式
  -r, --release       Release构建模式 (默认)
  -t, --tests         启用单元测试
  --vs2019            使用Visual Studio 2019
  --vs2022            使用Visual Studio 2022
  --clean             清理构建目录
  -v, --verbose       详细输出
```

### CMake选项

```bash
cmake [选项] ..

主要选项:
  -DCMAKE_BUILD_TYPE=Debug|Release
  -DBUILD_TESTS=ON|OFF
  -DBUILD_BENCHMARKS=ON|OFF
  -DENABLE_COVERAGE=ON|OFF
  -DENABLE_ASAN=ON|OFF
  -DENABLE_TSAN=ON|OFF
  -DENABLE_CLANG_TIDY=ON|OFF
```

## 使用指南

### 命令行参数

```bash
./augment_auth_server [选项]

选项:
  -h, --help              显示帮助信息
  -p, --port PORT         设置服务器端口 (默认: 3000)
  -H, --host HOST         设置服务器主机 (默认: localhost)
  -d, --debug             启用调试模式
  --no-cors               禁用CORS支持
  --session-path PATH     设置会话存储路径
  --client-secret SECRET  设置OAuth客户端密钥
```

### API端点

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/` | Web界面 |
| POST | `/api/start-auth` | 开始认证流程 |
| POST | `/api/complete-auth` | 完成认证流程 |
| GET | `/api/accounts` | 获取账户列表 |
| GET | `/api/current-account` | 获取当前账户 |
| POST | `/api/switch-account` | 切换账户 |
| DELETE | `/api/remove-account` | 移除账户 |
| POST | `/api/refresh-token` | 刷新token |
| GET | `/callback` | OAuth回调 |

### API使用示例

**开始认证流程:**
```bash
curl -X POST http://localhost:3000/api/start-auth
```

**完成认证流程:**
```bash
curl -X POST http://localhost:3000/api/complete-auth \
     -H 'Content-Type: application/json' \
     -d '{"callbackUrl": "vscode://augment.vscode-augment/auth/result?code=...&state=..."}'
```

**获取账户列表:**
```bash
curl http://localhost:3000/api/accounts
```

**切换账户:**
```bash
curl -X POST http://localhost:3000/api/switch-account \
     -H 'Content-Type: application/json' \
     -d '{"accountId": "<EMAIL>"}'
```

## 配置

### 环境变量

```bash
# 服务器配置
AUGMENT_SERVER_HOST=localhost
AUGMENT_SERVER_PORT=3000
AUGMENT_DEBUG_MODE=true

# 认证配置
AUGMENT_CLIENT_SECRET=your-secret-key
AUGMENT_SESSION_PATH=./sessions

# SSL配置
AUGMENT_SSL_CERT_PATH=/path/to/cert.pem
AUGMENT_SSL_KEY_PATH=/path/to/key.pem
```

### 配置文件

创建 `config.json` 文件：
```json
{
  "server": {
    "host": "localhost",
    "port": 3000,
    "enable_cors": true,
    "debug_mode": false
  },
  "auth": {
    "client_secret": "your-secret-key",
    "session_store_path": "./sessions"
  }
}
```

## 开发

### 项目结构

```
cpp/
├── auth_web_server.hpp/cpp          # Web服务器主类
├── augment_auth_manager.hpp/cpp     # 认证管理器
├── oauth_pkce_generator.hpp/cpp     # OAuth PKCE生成器
├── token_exchanger.hpp/cpp          # Token交换器
├── session_store.hpp/cpp            # 会话存储器
├── main.cpp                         # 主程序入口
├── CMakeLists.txt                   # CMake配置
├── config.hpp.in                    # 配置模板
├── build.sh                         # Linux/macOS构建脚本
├── build.bat                        # Windows构建脚本
└── README.md                        # 本文档
```

### 代码风格

- 遵循现代C++17最佳实践
- 使用RAII和智能指针
- 异常安全保证
- const正确性
- 命名约定：snake_case

### 测试

```bash
# 运行单元测试
./build.sh --tests
cd build && ctest

# 运行性能测试
./build.sh --benchmarks
cd build && ./benchmarks/auth_benchmarks

# 代码覆盖率
./build.sh --coverage
cd build && make coverage
```

## 故障排除

### 常见问题

**1. 编译错误：找不到OpenSSL**
```bash
# Ubuntu/Debian
sudo apt-get install libssl-dev

# CentOS/RHEL
sudo yum install openssl-devel

# macOS
brew install openssl
```

**2. 编译错误：找不到nlohmann/json**
```bash
# 系统安装
sudo apt-get install nlohmann-json3-dev

# 或让CMake自动下载（推荐）
cmake -DFETCH_DEPENDENCIES=ON ..
```

**3. 运行时错误：端口被占用**
```bash
# 使用不同端口
./augment_auth_server --port 8080

# 或查找占用进程
lsof -i :3000
```

**4. 认证失败：网络连接问题**
```bash
# 检查网络连接
curl -I https://auth.augmentcode.com

# 使用代理（如果需要）
export https_proxy=http://proxy.example.com:8080
```

### 调试

**启用调试模式:**
```bash
./augment_auth_server --debug
```

**使用调试器:**
```bash
# GDB
gdb ./augment_auth_server
(gdb) run --debug

# LLDB
lldb ./augment_auth_server
(lldb) run -- --debug
```

**内存检查:**
```bash
# 使用AddressSanitizer构建
./build.sh --asan

# 使用Valgrind
valgrind --leak-check=full ./augment_auth_server
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 更新日志

### v1.0.0 (2025-01-05)
- 初始C++实现
- 完整移植JavaScript功能
- 现代C++17特性支持
- 跨平台构建支持
