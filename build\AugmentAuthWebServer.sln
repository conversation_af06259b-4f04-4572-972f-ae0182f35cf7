﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{EBEC0D0E-BEF5-35CD-BF4D-4C1D3633D60B}"
	ProjectSection(ProjectDependencies) = postProject
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813} = {159D0DC8-7D24-39C9-AF44-EE2E361C3813}
		{979F0C53-36FD-3E94-9EE4-799A2A1FA5CB} = {979F0C53-36FD-3E94-9EE4-799A2A1FA5CB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{6F9D3AB9-34E6-31D6-A994-C9811840536D}"
	ProjectSection(ProjectDependencies) = postProject
		{EBEC0D0E-BEF5-35CD-BF4D-4C1D3633D60B} = {EBEC0D0E-BEF5-35CD-BF4D-4C1D3633D60B}
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813} = {159D0DC8-7D24-39C9-AF44-EE2E361C3813}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{66F7C5A2-A29D-3292-B799-016E9B4A1696}"
	ProjectSection(ProjectDependencies) = postProject
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813} = {159D0DC8-7D24-39C9-AF44-EE2E361C3813}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{159D0DC8-7D24-39C9-AF44-EE2E361C3813}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "augment_auth_server", "augment_auth_server.vcxproj", "{979F0C53-36FD-3E94-9EE4-799A2A1FA5CB}"
	ProjectSection(ProjectDependencies) = postProject
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813} = {159D0DC8-7D24-39C9-AF44-EE2E361C3813}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{EBEC0D0E-BEF5-35CD-BF4D-4C1D3633D60B}.Debug|x64.ActiveCfg = Debug|x64
		{EBEC0D0E-BEF5-35CD-BF4D-4C1D3633D60B}.Debug|x64.Build.0 = Debug|x64
		{EBEC0D0E-BEF5-35CD-BF4D-4C1D3633D60B}.Release|x64.ActiveCfg = Release|x64
		{EBEC0D0E-BEF5-35CD-BF4D-4C1D3633D60B}.Release|x64.Build.0 = Release|x64
		{EBEC0D0E-BEF5-35CD-BF4D-4C1D3633D60B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{EBEC0D0E-BEF5-35CD-BF4D-4C1D3633D60B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{EBEC0D0E-BEF5-35CD-BF4D-4C1D3633D60B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{EBEC0D0E-BEF5-35CD-BF4D-4C1D3633D60B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{6F9D3AB9-34E6-31D6-A994-C9811840536D}.Debug|x64.ActiveCfg = Debug|x64
		{6F9D3AB9-34E6-31D6-A994-C9811840536D}.Release|x64.ActiveCfg = Release|x64
		{6F9D3AB9-34E6-31D6-A994-C9811840536D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6F9D3AB9-34E6-31D6-A994-C9811840536D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{66F7C5A2-A29D-3292-B799-016E9B4A1696}.Debug|x64.ActiveCfg = Debug|x64
		{66F7C5A2-A29D-3292-B799-016E9B4A1696}.Release|x64.ActiveCfg = Release|x64
		{66F7C5A2-A29D-3292-B799-016E9B4A1696}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{66F7C5A2-A29D-3292-B799-016E9B4A1696}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813}.Debug|x64.ActiveCfg = Debug|x64
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813}.Debug|x64.Build.0 = Debug|x64
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813}.Release|x64.ActiveCfg = Release|x64
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813}.Release|x64.Build.0 = Release|x64
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{159D0DC8-7D24-39C9-AF44-EE2E361C3813}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{979F0C53-36FD-3E94-9EE4-799A2A1FA5CB}.Debug|x64.ActiveCfg = Debug|x64
		{979F0C53-36FD-3E94-9EE4-799A2A1FA5CB}.Debug|x64.Build.0 = Debug|x64
		{979F0C53-36FD-3E94-9EE4-799A2A1FA5CB}.Release|x64.ActiveCfg = Release|x64
		{979F0C53-36FD-3E94-9EE4-799A2A1FA5CB}.Release|x64.Build.0 = Release|x64
		{979F0C53-36FD-3E94-9EE4-799A2A1FA5CB}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{979F0C53-36FD-3E94-9EE4-799A2A1FA5CB}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{979F0C53-36FD-3E94-9EE4-799A2A1FA5CB}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{979F0C53-36FD-3E94-9EE4-799A2A1FA5CB}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {34F11BBB-C300-3710-8CBE-3374627CF202}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
