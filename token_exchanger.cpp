/**
 * @file token_exchanger.cpp
 * @brief Token交换器实现
 */

#include "token_exchanger.hpp"
#include <httplib.h>
#include <sstream>
#include <thread>
#include <chrono>
#include <regex>
#include <iostream>

using namespace augment;
using json = nlohmann::json;

// TokenData 实现
bool TokenData::is_expired() const noexcept {
    if (!expires_at) {
        return false; // 没有过期时间，认为不过期
    }
    
    const auto now = std::chrono::system_clock::now();
    return now >= *expires_at;
}

std::chrono::seconds TokenData::time_until_expiry() const noexcept {
    if (!expires_at) {
        return std::chrono::seconds::max(); // 没有过期时间
    }
    
    const auto now = std::chrono::system_clock::now();
    if (now >= *expires_at) {
        return std::chrono::seconds::zero(); // 已过期
    }
    
    return std::chrono::duration_cast<std::chrono::seconds>(*expires_at - now);
}

// TokenExchanger 实现
TokenExchanger::TokenExchanger(const TokenExchangerConfig& config)
    : config_(config) {
}

TokenExchanger::~TokenExchanger() = default;

std::future<TokenData> TokenExchanger::exchange_code_for_token(const CodeExchangeRequest& request) {
    return std::async(std::launch::async, [this, request]() -> TokenData {
        // 构建token端点URL
        const std::string token_url = get_token_endpoint(request.tenant_url);
        
        // 构建请求参数
        std::unordered_map<std::string, std::string> form_data = {
            {"grant_type", "authorization_code"},
            {"code", request.code},
            {"code_verifier", request.code_verifier},
            {"client_id", request.client_id},
            {"redirect_uri", request.redirect_uri}
        };
        
        // 添加客户端密钥（如果有）
        if (config_.client_secret) {
            form_data["client_secret"] = *config_.client_secret;
        }
        
        // 执行HTTP请求
        auto response_future = make_http_request(token_url, form_data);
        auto response_json = response_future.get();
        
        // 解析响应
        return parse_token_response(response_json);
    });
}

std::future<TokenData> TokenExchanger::refresh_token(const TokenRefreshRequest& request) {
    return std::async(std::launch::async, [this, request]() -> TokenData {
        // 构建token端点URL
        const std::string token_url = get_token_endpoint(request.tenant_url);
        
        // 构建请求参数
        std::unordered_map<std::string, std::string> form_data = {
            {"grant_type", "refresh_token"},
            {"refresh_token", request.refresh_token},
            {"client_id", request.client_id}
        };
        
        // 添加scope（如果有）
        if (request.scope) {
            form_data["scope"] = *request.scope;
        }
        
        // 添加客户端密钥（如果有）
        if (config_.client_secret) {
            form_data["client_secret"] = *config_.client_secret;
        }
        
        // 执行HTTP请求
        auto response_future = make_http_request(token_url, form_data);
        auto response_json = response_future.get();
        
        // 解析响应
        return parse_token_response(response_json);
    });
}

nlohmann::json TokenExchanger::parse_callback_url(const std::string& callback_url) {
    json result;
    
    // 解析URL
    std::regex url_regex(R"(^([^:/?#]+):(?://([^/?#]*))?([^?#]*)(?:\?([^#]*))?(?:#(.*))?$)");
    std::smatch url_match;
    
    if (!std::regex_match(callback_url, url_match, url_regex)) {
        throw TokenExchangeException("无效的回调URL格式");
    }
    
    const std::string query_string = url_match[4].str();
    
    // 解析查询参数
    std::istringstream iss(query_string);
    std::string pair;
    
    while (std::getline(iss, pair, '&')) {
        const auto eq_pos = pair.find('=');
        if (eq_pos != std::string::npos) {
            const std::string key = pair.substr(0, eq_pos);
            const std::string value = pair.substr(eq_pos + 1);
            
            // URL解码（简化版）
            std::string decoded_value = value;
            std::replace(decoded_value.begin(), decoded_value.end(), '+', ' ');
            
            result[key] = decoded_value;
        }
    }
    
    return result;
}

bool TokenExchanger::validate_state(const std::string& received_state, 
                                   const std::string& expected_state) {
    return received_state == expected_state;
}

bool TokenExchanger::validate_token_response(const nlohmann::json& response_json) {
    return response_json.contains("access_token") && 
           response_json.contains("token_type");
}

std::string TokenExchanger::get_token_endpoint(const std::string& tenant_url) {
    // 移除末尾的斜杠
    std::string clean_url = tenant_url;
    if (!clean_url.empty() && clean_url.back() == '/') {
        clean_url.pop_back();
    }
    
    return clean_url + "/oauth/token";
}

// 私有方法实现
std::future<nlohmann::json> TokenExchanger::make_http_request(
    const std::string& url,
    const std::unordered_map<std::string, std::string>& form_data,
    const std::unordered_map<std::string, std::string>& headers) {
    
    return std::async(std::launch::async, [this, url, form_data, headers]() -> json {
        // 解析URL
        std::regex url_regex(R"(^https?://([^/]+)(.*)$)");
        std::smatch url_match;
        
        if (!std::regex_match(url, url_match, url_regex)) {
            throw TokenExchangeException("无效的URL格式: " + url);
        }
        
        const std::string host = url_match[1].str();
        const std::string path = url_match[2].str();
        
        // 创建HTTP客户端
        httplib::Client client(("https://" + host).c_str());
        client.set_connection_timeout(config_.http.connect_timeout);
        client.set_read_timeout(config_.http.read_timeout);
        
        // 设置用户代理
        httplib::Headers request_headers = {
            {"User-Agent", config_.http.user_agent},
            {"Content-Type", "application/x-www-form-urlencoded"}
        };
        
        // 添加自定义头部
        for (const auto& [key, value] : headers) {
            request_headers.emplace(key, value);
        }
        
        // 构建表单数据
        const std::string body = build_form_data(form_data);
        
        // 执行POST请求
        auto response = client.Post(path.c_str(), request_headers, body, "application/x-www-form-urlencoded");
        
        if (!response) {
            throw TokenExchangeException("HTTP请求失败");
        }
        
        if (response->status != 200) {
            throw TokenExchangeException("HTTP错误: " + std::to_string(response->status) + " " + response->body);
        }
        
        // 解析JSON响应
        try {
            return json::parse(response->body);
        } catch (const std::exception& e) {
            throw TokenExchangeException("JSON解析失败: " + std::string(e.what()));
        }
    });
}

std::string TokenExchanger::build_form_data(
    const std::unordered_map<std::string, std::string>& form_data) {
    
    std::ostringstream oss;
    bool first = true;
    
    for (const auto& [key, value] : form_data) {
        if (!first) {
            oss << '&';
        }
        
        // 简化的URL编码
        oss << key << '=' << value;
        first = false;
    }
    
    return oss.str();
}

TokenData TokenExchanger::parse_token_response(const nlohmann::json& response_json) {
    if (!validate_token_response(response_json)) {
        throw TokenExchangeException("无效的token响应格式");
    }
    
    TokenData token_data;
    if (response_json.contains("access_token")) {
        token_data.access_token = response_json["access_token"].get<std::string>();
    }
    token_data.token_type = response_json.value("token_type", "Bearer");

    if (response_json.contains("refresh_token")) {
        token_data.refresh_token = response_json["refresh_token"].get<std::string>();
    }

    if (response_json.contains("scope")) {
        token_data.scope = response_json["scope"].get<std::string>();
    }

    if (response_json.contains("expires_in")) {
        const int expires_in = response_json["expires_in"].get<int>();
        token_data.expires_at = calculate_expiry_time(expires_in);
    }
    
    return token_data;
}

std::chrono::system_clock::time_point TokenExchanger::calculate_expiry_time(int expires_in) {
    const auto now = std::chrono::system_clock::now();
    return now + std::chrono::seconds(expires_in);
}

void TokenExchanger::log_debug(const std::string& message) const {
    if (config_.debug) {
        std::cout << "[DEBUG] TokenExchanger: " << message << std::endl;
    }
}

void TokenExchanger::log_error(const std::string& message) const {
    std::cerr << "[ERROR] TokenExchanger: " << message << std::endl;
}
