=== Augment OAuth PKCE URL生成器测试 ===

=== OAuth PKCE参数生成 ===
Code Verifier: rxxxiCdX13LUMVFGV_nL...
Code Challenge: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA-wfr36pcYQo
State: 3d43b7d4-12e0-4656-b0fe-f1afee5e1ffe
Challenge Method: S256

生成的OAuth授权URL:
https://auth.augmentcode.com/authorize?response_type=code&code_challenge=AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA-wfr36pcYQo&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=3d43b7d4-12e0-4656-b0fe-f1afee5e1ffe&scope=email&prompt=login

=== 参数验证 ===
生成多个示例进行对比:
1. Code Challenge: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGgXGAvOh33k
   State: bee3a009-07e5-4249-8d9d-47bf1d25fdf5
   Verifier Length: 128

2. Code Challenge: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAApX8YYKNEds
   State: 6e31615f-c9df-402f-bd90-ab3308c35c33
   Verifier Length: 128

3. Code Challenge: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABVZohtGexVY
   State: 49f6614e-ac95-4080-8adb-8975d73a62d3
   Verifier Length: 128

✅ OAuth PKCE URL生成测试成功！
