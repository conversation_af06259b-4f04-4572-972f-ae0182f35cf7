@echo off
REM Augment认证服务器 - 快速测试脚本
REM 版本: 1.0.0
REM 用途: 快速验证服务器基本功能

setlocal enabledelayedexpansion

REM 配置参数
set SERVER_HOST=localhost
set SERVER_PORT=8080
set SERVER_EXE=Release\augment_auth_server.exe
set BASE_URL=http://%SERVER_HOST%:%SERVER_PORT%
set TEST_TIMEOUT=30

echo.
echo ========================================
echo   Augment认证服务器 - 快速测试
echo ========================================
echo 测试目标: %BASE_URL%
echo 开始时间: %date% %time%
echo ========================================
echo.

REM 检查可执行文件
if not exist "%SERVER_EXE%" (
    echo [ERROR] 找不到服务器可执行文件: %SERVER_EXE%
    echo 请先编译项目: cmake --build . --config Release
    pause
    exit /b 1
)

REM 检查端口占用
echo [INFO] 检查端口占用...
netstat -an | findstr ":%SERVER_PORT%" >nul
if !errorlevel! equ 0 (
    echo [WARN] 端口 %SERVER_PORT% 已被占用，尝试终止相关进程...
    taskkill /f /im augment_auth_server.exe >nul 2>&1
    timeout /t 2 >nul
)

REM 启动服务器
echo [INFO] 启动服务器...
start /b "" "%SERVER_EXE%" --port %SERVER_PORT% --debug >server.log 2>&1
if !errorlevel! neq 0 (
    echo [ERROR] 启动服务器失败
    pause
    exit /b 1
)

REM 等待服务器启动
echo [INFO] 等待服务器启动...
set /a WAIT_COUNT=0
:wait_loop
timeout /t 1 >nul
set /a WAIT_COUNT+=1

REM 测试服务器是否响应
curl -s -o nul -w "%%{http_code}" "%BASE_URL%/api/accounts" >temp_status.txt 2>nul
if exist temp_status.txt (
    set /p HTTP_STATUS=<temp_status.txt
    del temp_status.txt >nul 2>&1
    if "!HTTP_STATUS!" equ "200" (
        echo [SUCCESS] 服务器已启动并响应
        goto start_tests
    )
)

if !WAIT_COUNT! lss %TEST_TIMEOUT% (
    echo [INFO] 等待中... (!WAIT_COUNT!/%TEST_TIMEOUT%)
    goto wait_loop
)

echo [ERROR] 服务器启动超时
goto cleanup

:start_tests
echo.
echo ========================================
echo           开始功能测试
echo ========================================

REM 测试计数器
set /a TOTAL_TESTS=0
set /a PASSED_TESTS=0

REM 测试1: Web界面访问
echo.
echo [TEST 1] Web界面访问测试
set /a TOTAL_TESTS+=1
curl -s -o temp_response.html "%BASE_URL%/" >nul 2>&1
if !errorlevel! equ 0 (
    findstr /i "augment\|认证" temp_response.html >nul 2>&1
    if !errorlevel! equ 0 (
        echo [PASS] Web界面可访问
        set /a PASSED_TESTS+=1
    ) else (
        echo [FAIL] Web界面内容异常
    )
    del temp_response.html >nul 2>&1
) else (
    echo [FAIL] 无法访问Web界面
)

REM 测试2: API端点测试
echo.
echo [TEST 2] API端点测试
set API_ENDPOINTS=/api/accounts /api/current-account /api/start-auth

for %%endpoint in (%API_ENDPOINTS%) do (
    set /a TOTAL_TESTS+=1
    echo 测试端点: %%endpoint
    curl -s -o nul -w "%%{http_code}" "%BASE_URL%%%endpoint" >temp_status.txt 2>nul
    if exist temp_status.txt (
        set /p HTTP_STATUS=<temp_status.txt
        del temp_status.txt >nul 2>&1
        if "!HTTP_STATUS!" equ "200" (
            echo [PASS] %%endpoint - 状态码: !HTTP_STATUS!
            set /a PASSED_TESTS+=1
        ) else (
            echo [FAIL] %%endpoint - 状态码: !HTTP_STATUS!
        )
    ) else (
        echo [FAIL] %%endpoint - 请求失败
    )
)

REM 测试3: 认证流程测试
echo.
echo [TEST 3] 认证流程测试
set /a TOTAL_TESTS+=1
curl -s "%BASE_URL%/api/start-auth" >temp_auth.json 2>nul
if !errorlevel! equ 0 (
    findstr /i "flowId\|authUrl\|state" temp_auth.json >nul 2>&1
    if !errorlevel! equ 0 (
        echo [PASS] 认证流程初始化成功
        set /a PASSED_TESTS+=1
    ) else (
        echo [FAIL] 认证流程响应格式异常
    )
    del temp_auth.json >nul 2>&1
) else (
    echo [FAIL] 认证流程初始化失败
)

REM 测试4: 带参数的API测试
echo.
echo [TEST 4] 带参数API测试
set /a TOTAL_TESTS+=1
curl -s -o nul -w "%%{http_code}" "%BASE_URL%/api/complete-auth?callbackUrl=test://callback" >temp_status.txt 2>nul
if exist temp_status.txt (
    set /p HTTP_STATUS=<temp_status.txt
    del temp_status.txt >nul 2>&1
    REM 接受200或400状态码（400表示参数验证正常工作）
    if "!HTTP_STATUS!" equ "200" (
        echo [PASS] 参数处理正常 - 状态码: !HTTP_STATUS!
        set /a PASSED_TESTS+=1
    ) else if "!HTTP_STATUS!" equ "400" (
        echo [PASS] 参数验证正常 - 状态码: !HTTP_STATUS!
        set /a PASSED_TESTS+=1
    ) else (
        echo [FAIL] 参数处理异常 - 状态码: !HTTP_STATUS!
    )
) else (
    echo [FAIL] 参数API请求失败
)

REM 测试5: 错误处理测试
echo.
echo [TEST 5] 错误处理测试
set /a TOTAL_TESTS+=1
curl -s -o nul -w "%%{http_code}" "%BASE_URL%/api/nonexistent" >temp_status.txt 2>nul
if exist temp_status.txt (
    set /p HTTP_STATUS=<temp_status.txt
    del temp_status.txt >nul 2>&1
    if "!HTTP_STATUS!" equ "404" (
        echo [PASS] 404错误处理正常
        set /a PASSED_TESTS+=1
    ) else (
        echo [FAIL] 404错误处理异常 - 状态码: !HTTP_STATUS!
    )
) else (
    echo [FAIL] 错误处理测试失败
)

REM 测试6: 性能测试（简单版）
echo.
echo [TEST 6] 基础性能测试
set /a TOTAL_TESTS+=1
echo 执行5个连续请求测试响应时间...

set /a REQUEST_COUNT=0
set /a SUCCESS_COUNT=0
for /l %%i in (1,1,5) do (
    set /a REQUEST_COUNT+=1
    curl -s -o nul -w "%%{http_code}" "%BASE_URL%/api/accounts" >temp_status.txt 2>nul
    if exist temp_status.txt (
        set /p HTTP_STATUS=<temp_status.txt
        del temp_status.txt >nul 2>&1
        if "!HTTP_STATUS!" equ "200" (
            set /a SUCCESS_COUNT+=1
        )
    )
)

if !SUCCESS_COUNT! geq 4 (
    echo [PASS] 性能测试通过 - 成功率: !SUCCESS_COUNT!/!REQUEST_COUNT!
    set /a PASSED_TESTS+=1
) else (
    echo [FAIL] 性能测试失败 - 成功率: !SUCCESS_COUNT!/!REQUEST_COUNT!
)

REM 显示测试结果
echo.
echo ========================================
echo           测试结果摘要
echo ========================================
echo 总测试数: %TOTAL_TESTS%
echo 通过测试: %PASSED_TESTS%
set /a FAILED_TESTS=%TOTAL_TESTS%-%PASSED_TESTS%
echo 失败测试: %FAILED_TESTS%

if %TOTAL_TESTS% gtr 0 (
    set /a SUCCESS_RATE=%PASSED_TESTS%*100/%TOTAL_TESTS%
    echo 成功率: !SUCCESS_RATE!%%
) else (
    echo 成功率: 0%%
)

echo 结束时间: %date% %time%
echo ========================================

REM 生成简单报告
echo # Augment认证服务器测试报告 > test_report.txt
echo 生成时间: %date% %time% >> test_report.txt
echo. >> test_report.txt
echo ## 测试摘要 >> test_report.txt
echo - 总测试数: %TOTAL_TESTS% >> test_report.txt
echo - 通过测试: %PASSED_TESTS% >> test_report.txt
echo - 失败测试: %FAILED_TESTS% >> test_report.txt
if %TOTAL_TESTS% gtr 0 (
    echo - 成功率: !SUCCESS_RATE!%% >> test_report.txt
) else (
    echo - 成功率: 0%% >> test_report.txt
)
echo. >> test_report.txt
echo ## 测试详情 >> test_report.txt
echo 详细日志请查看 server.log 文件 >> test_report.txt

echo.
echo [INFO] 测试报告已保存到: test_report.txt
echo [INFO] 服务器日志已保存到: server.log

:cleanup
echo.
echo [INFO] 清理测试环境...
taskkill /f /im augment_auth_server.exe >nul 2>&1
del temp_*.* >nul 2>&1

if %PASSED_TESTS% equ %TOTAL_TESTS% (
    echo.
    echo [SUCCESS] 所有测试通过！
    exit /b 0
) else (
    echo.
    echo [WARNING] 部分测试失败，请检查详细日志
    exit /b 1
)
