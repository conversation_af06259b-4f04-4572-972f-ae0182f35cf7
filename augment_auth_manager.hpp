/**
 * @file augment_auth_manager.hpp
 * @brief Augment认证管理器 - C++17实现
 * <AUTHOR> Agent
 * @date 2025-01-05
 * 
 * 现代C++实现的OAuth认证管理器
 * 负责OAuth流程编排、token管理和账户切换
 */

#pragma once

#include <string>
#include <string_view>
#include <memory>
#include <unordered_map>
#include <vector>
#include <optional>
#include <chrono>
#include <future>
#include <mutex>

// 第三方库依赖
#ifdef HAVE_NLOHMANN_JSON
#include <nlohmann/json.hpp>
#else
#include "simple_json.hpp"
#endif

// 前向声明
namespace augment {
    class TokenExchanger;
    class SessionStore;
    class OAuthPKCEGenerator;
}

namespace augment {

/**
 * @brief 认证异常类
 */
class AuthException : public std::runtime_error {
public:
    explicit AuthException(const std::string& message)
        : std::runtime_error("认证错误: " + message) {}
};

/**
 * @brief 认证流程数据结构
 */
struct AuthFlowData {
    std::string flow_id;
    std::string auth_url;
    std::string state;
    std::string code_verifier;
    std::string code_challenge;
    std::chrono::system_clock::time_point created_at;
    std::string instructions;
};

/**
 * @brief 账户信息结构
 */
struct AccountInfo {
    std::string account_id;
    std::string email;
    std::string tenant_url;
    std::string access_token;
    std::string refresh_token;
    std::string token_type;
    std::optional<std::chrono::system_clock::time_point> expires_at;
    std::string scope;
    std::chrono::system_clock::time_point last_used;
    bool is_expired = false;
};

/**
 * @brief 用户信息结构
 */
struct UserInfo {
    std::string email;
    std::string name;
    std::optional<std::string> id;
};

/**
 * @brief 认证结果结构
 */
struct AuthResult {
    std::string account_id;
    std::string email;
    std::string tenant_url;
    bool success = false;
};

/**
 * @brief 认证管理器配置
 */
struct AuthManagerConfig {
    bool debug = false;
    std::string client_secret;
    std::string session_store_path = "./sessions";
    std::chrono::minutes pending_auth_timeout{10};
    std::chrono::seconds token_refresh_threshold{300}; // 5分钟
};

/**
 * @brief Augment认证管理器类
 * 
 * 主要认证管理器，负责编排OAuth流程、token管理和账户切换
 * 支持PKCE、多账户管理、自动token刷新等功能
 */
class AugmentAuthManager {
public:
    /**
     * @brief 构造函数
     * @param config 认证管理器配置
     */
    explicit AugmentAuthManager(const AuthManagerConfig& config = {});
    
    /**
     * @brief 析构函数
     */
    ~AugmentAuthManager();
    
    // 禁用拷贝，允许移动
    AugmentAuthManager(const AugmentAuthManager&) = delete;
    AugmentAuthManager& operator=(const AugmentAuthManager&) = delete;
    AugmentAuthManager(AugmentAuthManager&&) = default;
    AugmentAuthManager& operator=(AugmentAuthManager&&) = default;
    
    /**
     * @brief 初始化认证管理器
     * @return 初始化完成的Future
     * @throws AuthException 初始化失败时
     */
    [[nodiscard]] std::future<void> initialize();
    
    /**
     * @brief 开始新的认证流程
     * @param options 认证选项（可选）
     * @return 认证流程数据
     * @throws AuthException 启动失败时
     */
    [[nodiscard]] std::future<AuthFlowData> start_auth_flow(const nlohmann::json& options = {});
    
    /**
     * @brief 使用回调URL完成认证流程
     * @param callback_url vscode://回调URL
     * @return 认证结果
     * @throws AuthException 完成失败时
     */
    [[nodiscard]] std::future<AuthResult> complete_auth_flow(const std::string& callback_url);
    
    /**
     * @brief 列出所有存储的账户
     * @return 账户信息列表
     */
    [[nodiscard]] std::future<std::vector<AccountInfo>> list_accounts();
    
    /**
     * @brief 切换到不同的账户
     * @param account_id 账户标识符
     * @return 切换结果
     * @throws AuthException 切换失败时
     */
    [[nodiscard]] std::future<AccountInfo> switch_account(const std::string& account_id);
    
    /**
     * @brief 获取当前活跃账户
     * @return 当前账户数据或nullopt
     */
    [[nodiscard]] std::future<std::optional<AccountInfo>> get_current_account();
    
    /**
     * @brief 刷新账户的token
     * @param account_id 账户标识符
     * @return 刷新结果
     * @throws AuthException 刷新失败时
     */
    [[nodiscard]] std::future<bool> refresh_account_token(const std::string& account_id);
    
    /**
     * @brief 移除账户
     * @param account_id 账户标识符
     * @throws AuthException 移除失败时
     */
    [[nodiscard]] std::future<void> remove_account(const std::string& account_id);
    
    /**
     * @brief 获取当前账户的有效访问token
     * @return 有效的访问token
     * @throws AuthException 无当前账户或token无效时
     */
    [[nodiscard]] std::future<std::string> get_current_access_token();
    
    /**
     * @brief 验证token是否过期
     * @param expires_at 过期时间点
     * @return 如果已过期则返回true
     */
    [[nodiscard]] static bool is_token_expired(
        const std::optional<std::chrono::system_clock::time_point>& expires_at) noexcept;
    
    /**
     * @brief 清理过期的待处理认证
     */
    void cleanup_pending_authorizations();

private:
    /**
     * @brief 获取用户信息
     * @param access_token 访问token
     * @param tenant_url 租户URL
     * @return 用户信息
     */
    [[nodiscard]] std::future<UserInfo> get_user_info(const std::string& access_token, 
                                                      const std::string& tenant_url);
    
    /**
     * @brief 从JWT token中提取邮箱（如果适用）
     * @param token 访问token
     * @return 邮箱或nullopt
     */
    [[nodiscard]] std::optional<std::string> extract_email_from_token(const std::string& token) const;
    
    /**
     * @brief 生成账户ID
     * @param user_info 用户信息
     * @return 账户ID
     */
    [[nodiscard]] std::string generate_account_id(const UserInfo& user_info) const;
    
    /**
     * @brief 验证认证流程状态
     * @param state 状态参数
     * @return 如果状态有效则返回true
     */
    [[nodiscard]] bool validate_auth_state(const std::string& state) const;
    
    /**
     * @brief 记录调试信息
     * @param message 调试消息
     */
    void log_debug(const std::string& message) const;
    
    /**
     * @brief 记录信息
     * @param message 信息消息
     */
    void log_info(const std::string& message) const;
    
    /**
     * @brief 记录错误
     * @param message 错误消息
     */
    void log_error(const std::string& message) const;

private:
    AuthManagerConfig config_;
    std::unique_ptr<TokenExchanger> token_exchanger_;
    std::unique_ptr<SessionStore> session_store_;
    std::unique_ptr<OAuthPKCEGenerator> oauth_generator_;
    
    // 待处理的认证流程
    std::unordered_map<std::string, AuthFlowData> pending_authorizations_;
    mutable std::mutex pending_auth_mutex_;
    
    // 初始化状态
    std::atomic<bool> initialized_{false};
    mutable std::mutex init_mutex_;
};

/**
 * @brief 认证管理器构建器类
 * 
 * 提供流式API来配置和创建AugmentAuthManager实例
 */
class AugmentAuthManagerBuilder {
public:
    AugmentAuthManagerBuilder() = default;
    
    AugmentAuthManagerBuilder& debug(bool enable = true) {
        config_.debug = enable;
        return *this;
    }
    
    AugmentAuthManagerBuilder& client_secret(const std::string& secret) {
        config_.client_secret = secret;
        return *this;
    }
    
    AugmentAuthManagerBuilder& session_store_path(const std::string& path) {
        config_.session_store_path = path;
        return *this;
    }
    
    AugmentAuthManagerBuilder& pending_auth_timeout(std::chrono::minutes timeout) {
        config_.pending_auth_timeout = timeout;
        return *this;
    }
    
    AugmentAuthManagerBuilder& token_refresh_threshold(std::chrono::seconds threshold) {
        config_.token_refresh_threshold = threshold;
        return *this;
    }
    
    [[nodiscard]] std::unique_ptr<AugmentAuthManager> build() {
        return std::make_unique<AugmentAuthManager>(config_);
    }

private:
    AuthManagerConfig config_;
};

} // namespace augment
