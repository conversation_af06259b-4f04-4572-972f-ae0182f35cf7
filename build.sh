#!/bin/bash

# Augment认证Web服务器 - 构建脚本
# 支持Linux/macOS系统

set -e  # 遇到错误立即退出

echo "=== Augment认证Web服务器 - C++构建脚本 ==="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v "$1" &> /dev/null; then
        print_error "未找到命令: $1"
        return 1
    fi
    return 0
}

# 检查编译器
print_info "检查编译器..."
if check_command g++; then
    CXX_COMPILER="g++"
    print_success "找到编译器: $(g++ --version | head -n1)"
elif check_command clang++; then
    CXX_COMPILER="clang++"
    print_success "找到编译器: $(clang++ --version | head -n1)"
else
    print_error "未找到C++编译器 (g++或clang++)"
    echo "请安装GCC或Clang编译器套件"
    exit 1
fi

# 检查CMake
print_info "检查CMake..."
if ! check_command cmake; then
    print_error "未找到CMake"
    echo "Ubuntu/Debian: sudo apt-get install cmake"
    echo "CentOS/RHEL: sudo yum install cmake"
    echo "macOS: brew install cmake"
    exit 1
fi

CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
print_success "找到CMake: $CMAKE_VERSION"

# 检查依赖库
print_info "检查依赖库..."

# 检查OpenSSL
if ! pkg-config --exists openssl; then
    print_error "未找到OpenSSL库"
    echo "Ubuntu/Debian: sudo apt-get install libssl-dev"
    echo "CentOS/RHEL: sudo yum install openssl-devel"
    echo "macOS: brew install openssl"
    exit 1
fi

OPENSSL_VERSION=$(pkg-config --modversion openssl)
print_success "找到OpenSSL: $OPENSSL_VERSION"

# 检查线程库
if ! pkg-config --exists threads; then
    print_warning "未找到threads pkg-config，将使用系统默认"
fi

# 检查nlohmann/json (可选)
if pkg-config --exists nlohmann_json; then
    JSON_VERSION=$(pkg-config --modversion nlohmann_json)
    print_success "找到nlohmann/json: $JSON_VERSION"
else
    print_warning "未找到系统安装的nlohmann/json，将自动下载"
fi

# 检查httplib (可选)
if [ -f "/usr/include/httplib.h" ] || [ -f "/usr/local/include/httplib.h" ]; then
    print_success "找到httplib头文件"
else
    print_warning "未找到系统安装的httplib，将自动下载"
fi

# 解析命令行参数
BUILD_TYPE="Release"
ENABLE_TESTS=OFF
ENABLE_BENCHMARKS=OFF
ENABLE_COVERAGE=OFF
ENABLE_ASAN=OFF
ENABLE_TSAN=OFF
ENABLE_CLANG_TIDY=OFF
CLEAN_BUILD=false
INSTALL_DEPS=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        -r|--release)
            BUILD_TYPE="Release"
            shift
            ;;
        -t|--tests)
            ENABLE_TESTS=ON
            shift
            ;;
        -b|--benchmarks)
            ENABLE_BENCHMARKS=ON
            shift
            ;;
        -c|--coverage)
            ENABLE_COVERAGE=ON
            BUILD_TYPE="Debug"
            shift
            ;;
        --asan)
            ENABLE_ASAN=ON
            BUILD_TYPE="Debug"
            shift
            ;;
        --tsan)
            ENABLE_TSAN=ON
            BUILD_TYPE="Debug"
            shift
            ;;
        --clang-tidy)
            ENABLE_CLANG_TIDY=ON
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --install-deps)
            INSTALL_DEPS=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo
            echo "选项:"
            echo "  -d, --debug         Debug构建模式"
            echo "  -r, --release       Release构建模式 (默认)"
            echo "  -t, --tests         启用单元测试"
            echo "  -b, --benchmarks    启用性能测试"
            echo "  -c, --coverage      启用代码覆盖率 (自动启用Debug)"
            echo "  --asan              启用AddressSanitizer (自动启用Debug)"
            echo "  --tsan              启用ThreadSanitizer (自动启用Debug)"
            echo "  --clang-tidy        启用clang-tidy静态分析"
            echo "  --clean             清理构建目录"
            echo "  --install-deps      尝试安装依赖库"
            echo "  -v, --verbose       详细输出"
            echo "  -h, --help          显示此帮助信息"
            echo
            echo "示例:"
            echo "  $0                  # 基本Release构建"
            echo "  $0 --debug --tests  # Debug构建并启用测试"
            echo "  $0 --coverage       # 启用代码覆盖率分析"
            echo "  $0 --asan --tsan    # 启用内存和线程检查"
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 安装依赖库 (如果请求)
if [ "$INSTALL_DEPS" = true ]; then
    print_info "尝试安装依赖库..."
    
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y libssl-dev nlohmann-json3-dev
    elif command -v yum &> /dev/null; then
        sudo yum install -y openssl-devel nlohmann-json-devel
    elif command -v brew &> /dev/null; then
        brew install openssl nlohmann-json
    else
        print_warning "无法自动安装依赖库，请手动安装"
    fi
fi

# 创建构建目录
BUILD_DIR="build"
if [ "$CLEAN_BUILD" = true ] && [ -d "$BUILD_DIR" ]; then
    print_info "清理构建目录..."
    rm -rf "$BUILD_DIR"
fi

if [ ! -d "$BUILD_DIR" ]; then
    mkdir "$BUILD_DIR"
fi

cd "$BUILD_DIR"

# 构建配置
print_info "配置构建..."
echo "构建类型: $BUILD_TYPE"
echo "启用测试: $ENABLE_TESTS"
echo "启用性能测试: $ENABLE_BENCHMARKS"
echo "启用覆盖率: $ENABLE_COVERAGE"
echo "启用ASAN: $ENABLE_ASAN"
echo "启用TSAN: $ENABLE_TSAN"
echo "启用clang-tidy: $ENABLE_CLANG_TIDY"
echo

# CMake配置命令
CMAKE_ARGS=(
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE"
    -DCMAKE_CXX_COMPILER="$CXX_COMPILER"
    -DBUILD_TESTS="$ENABLE_TESTS"
    -DBUILD_BENCHMARKS="$ENABLE_BENCHMARKS"
    -DENABLE_COVERAGE="$ENABLE_COVERAGE"
    -DENABLE_ASAN="$ENABLE_ASAN"
    -DENABLE_TSAN="$ENABLE_TSAN"
    -DENABLE_CLANG_TIDY="$ENABLE_CLANG_TIDY"
)

if [ "$VERBOSE" = true ]; then
    CMAKE_ARGS+=(-DCMAKE_VERBOSE_MAKEFILE=ON)
fi

# 执行CMake配置
cmake "${CMAKE_ARGS[@]}" ..

if [ $? -eq 0 ]; then
    print_success "CMake配置成功"
else
    print_error "CMake配置失败"
    exit 1
fi

# 构建项目
print_info "开始构建..."

# 检测CPU核心数
if command -v nproc &> /dev/null; then
    JOBS=$(nproc)
elif command -v sysctl &> /dev/null; then
    JOBS=$(sysctl -n hw.ncpu)
else
    JOBS=4
fi

print_info "使用 $JOBS 个并行任务构建..."

if [ "$VERBOSE" = true ]; then
    make -j"$JOBS" VERBOSE=1
else
    make -j"$JOBS"
fi

if [ $? -eq 0 ]; then
    print_success "构建成功！"
else
    print_error "构建失败"
    exit 1
fi

# 运行测试 (如果启用)
if [ "$ENABLE_TESTS" = "ON" ]; then
    print_info "运行测试..."
    ctest --output-on-failure
fi

# 显示构建结果
echo
print_success "构建完成！"
echo
echo "可执行文件: $(pwd)/augment_auth_server"
echo
echo "运行服务器:"
echo "  ./augment_auth_server --help"
echo "  ./augment_auth_server --port 8080 --debug"
echo
echo "安装到系统:"
echo "  sudo make install"
echo

# 显示二进制文件信息
if [ -f "augment_auth_server" ]; then
    echo "二进制文件信息:"
    ls -lh augment_auth_server
    file augment_auth_server
    
    if command -v ldd &> /dev/null; then
        echo
        echo "依赖库:"
        ldd augment_auth_server | head -10
    fi
fi

print_success "🎉 构建脚本执行完成！"
