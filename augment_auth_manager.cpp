/**
 * @file augment_auth_manager.cpp
 * @brief Augment认证管理器实现
 */

#include "augment_auth_manager.hpp"
#include "token_exchanger.hpp"
#include "session_store.hpp"
#include "oauth_pkce_generator.hpp"
#include <iostream>
#include <sstream>
#include <regex>

using namespace augment;
using json = nlohmann::json;

// AugmentAuthManager 实现
AugmentAuthManager::AugmentAuthManager(const AuthManagerConfig& config)
    : config_(config) {
    
    // 创建组件实例
    TokenExchangerConfig token_config;
    token_config.debug = config.debug;
    token_config.client_secret = config.client_secret;
    token_exchanger_ = std::make_unique<TokenExchanger>(token_config);
    
    SessionStoreConfig session_config;
    session_config.storage_path = config.session_store_path;
    session_store_ = std::make_unique<SessionStore>(session_config);
    
    oauth_generator_ = std::make_unique<OAuthPKCEGenerator>();
}

AugmentAuthManager::~AugmentAuthManager() = default;

std::future<void> AugmentAuthManager::initialize() {
    return std::async(std::launch::async, [this]() {
        std::lock_guard<std::mutex> lock(init_mutex_);
        
        if (initialized_.load()) {
            return;
        }
        
        log_info("初始化认证管理器...");
        
        // 初始化会话存储
        auto session_init_future = session_store_->initialize();
        session_init_future.get();
        
        log_info("认证管理器初始化完成");
        initialized_.store(true);
    });
}

std::future<AuthFlowData> AugmentAuthManager::start_auth_flow(const nlohmann::json& options) {
    return std::async(std::launch::async, [this, options]() -> AuthFlowData {
        if (!initialized_.load()) {
            throw AuthException("认证管理器未初始化");
        }
        
        log_debug("开始新的认证流程");
        
        // 生成授权URL
        auto url_data = oauth_generator_->generate_authorization_url();
        
        // 创建认证流程数据
        AuthFlowData flow_data;
        flow_data.flow_id = url_data.state; // 使用state作为flow_id
        flow_data.auth_url = url_data.url;
        flow_data.state = url_data.state;
        flow_data.code_verifier = url_data.code_verifier;
        flow_data.code_challenge = url_data.code_challenge;
        flow_data.created_at = std::chrono::system_clock::now();
        flow_data.instructions = "Please complete authentication in your browser, then return to VSCode";
        
        // 存储待处理的认证流程
        {
            std::lock_guard<std::mutex> lock(pending_auth_mutex_);
            pending_authorizations_[flow_data.flow_id] = flow_data;
        }
        
        log_debug("认证流程已创建: " + flow_data.flow_id);
        
        return flow_data;
    });
}

std::future<AuthResult> AugmentAuthManager::complete_auth_flow(const std::string& callback_url) {
    return std::async(std::launch::async, [this, callback_url]() -> AuthResult {
        if (!initialized_.load()) {
            throw AuthException("认证管理器未初始化");
        }
        
        log_debug("完成认证流程: " + callback_url);
        
        // 解析回调URL
        auto callback_data = OAuthPKCEGenerator::parse_callback_url(callback_url);
        
        // 检查错误
        if (callback_data.error) {
            throw AuthException("认证失败: " + *callback_data.error + 
                              (callback_data.error_description ? " - " + *callback_data.error_description : ""));
        }
        
        // 验证必需参数
        if (callback_data.code.empty() || callback_data.state.empty()) {
            throw AuthException("回调URL缺少必需参数");
        }
        
        // 查找对应的认证流程
        AuthFlowData flow_data;
        {
            std::lock_guard<std::mutex> lock(pending_auth_mutex_);
            auto it = pending_authorizations_.find(callback_data.state);
            if (it == pending_authorizations_.end()) {
                throw AuthException("无效的认证状态");
            }
            flow_data = it->second;
            pending_authorizations_.erase(it);
        }
        
        // 验证state参数
        if (!validate_auth_state(callback_data.state)) {
            throw AuthException("认证状态验证失败");
        }
        
        // 交换授权码为访问token
        CodeExchangeRequest exchange_request;
        exchange_request.code = callback_data.code;
        exchange_request.code_verifier = flow_data.code_verifier;
        exchange_request.tenant_url = callback_data.tenant_url;
        
        auto token_future = token_exchanger_->exchange_code_for_token(exchange_request);
        auto token_data = token_future.get();
        
        // 获取用户信息
        auto user_info_future = get_user_info(token_data.access_token, callback_data.tenant_url);
        auto user_info = user_info_future.get();
        
        // 生成账户ID
        const std::string account_id = generate_account_id(user_info);
        
        // 创建会话数据
        SessionData session;
        session.account_id = account_id;
        session.email = user_info.email;
        session.tenant_url = callback_data.tenant_url;
        session.access_token = token_data.access_token;
        session.refresh_token = token_data.refresh_token;
        session.token_type = token_data.token_type;
        session.expires_at = token_data.expires_at;
        session.scope = token_data.scope;
        session.created_at = std::chrono::system_clock::now();
        session.last_used = std::chrono::system_clock::now();
        
        // 存储会话
        auto store_future = session_store_->store_session(session);
        store_future.get();
        
        // 设置为当前账户
        auto set_current_future = session_store_->set_current_account(account_id);
        set_current_future.get();
        
        log_info("认证完成: " + user_info.email);
        
        // 返回认证结果
        AuthResult result;
        result.account_id = account_id;
        result.email = user_info.email;
        result.tenant_url = callback_data.tenant_url;
        result.success = true;
        
        return result;
    });
}

std::future<std::vector<AccountInfo>> AugmentAuthManager::list_accounts() {
    return std::async(std::launch::async, [this]() -> std::vector<AccountInfo> {
        if (!initialized_.load()) {
            throw AuthException("认证管理器未初始化");
        }
        
        auto summaries_future = session_store_->list_sessions();
        auto summaries = summaries_future.get();
        
        std::vector<AccountInfo> accounts;
        accounts.reserve(summaries.size());
        
        for (const auto& summary : summaries) {
            // 获取完整的会话数据
            auto session_future = session_store_->get_session(summary.account_id);
            auto session_opt = session_future.get();
            
            if (session_opt) {
                const auto& session = *session_opt;
                
                AccountInfo account;
                account.account_id = session.account_id;
                account.email = session.email;
                account.tenant_url = session.tenant_url;
                account.access_token = session.access_token;
                account.refresh_token = session.refresh_token;
                account.token_type = session.token_type;
                account.expires_at = session.expires_at;
                account.scope = session.scope;
                account.last_used = session.last_used;
                account.is_expired = session.is_expired();
                
                accounts.push_back(account);
            }
        }
        
        return accounts;
    });
}

std::future<AccountInfo> AugmentAuthManager::switch_account(const std::string& account_id) {
    return std::async(std::launch::async, [this, account_id]() -> AccountInfo {
        if (!initialized_.load()) {
            throw AuthException("认证管理器未初始化");
        }
        
        // 获取账户会话
        auto session_future = session_store_->get_session(account_id);
        auto session_opt = session_future.get();
        
        if (!session_opt) {
            throw AuthException("账户不存在: " + account_id);
        }
        
        auto session = *session_opt;
        
        // 更新最后使用时间
        session.update_last_used();
        auto update_future = session_store_->store_session(session);
        update_future.get();
        
        // 设置为当前账户
        auto set_current_future = session_store_->set_current_account(account_id);
        set_current_future.get();
        
        log_info("切换到账户: " + session.email);
        
        // 转换为AccountInfo
        AccountInfo account;
        account.account_id = session.account_id;
        account.email = session.email;
        account.tenant_url = session.tenant_url;
        account.access_token = session.access_token;
        account.refresh_token = session.refresh_token;
        account.token_type = session.token_type;
        account.expires_at = session.expires_at;
        account.scope = session.scope;
        account.last_used = session.last_used;
        account.is_expired = session.is_expired();
        
        return account;
    });
}

std::future<std::optional<AccountInfo>> AugmentAuthManager::get_current_account() {
    return std::async(std::launch::async, [this]() -> std::optional<AccountInfo> {
        if (!initialized_.load()) {
            return std::nullopt;
        }
        
        // 获取当前账户ID
        auto current_future = session_store_->get_current_account();
        auto current_account_id = current_future.get();
        
        if (!current_account_id) {
            return std::nullopt;
        }
        
        // 获取账户信息
        auto session_future = session_store_->get_session(*current_account_id);
        auto session_opt = session_future.get();
        
        if (!session_opt) {
            return std::nullopt;
        }
        
        const auto& session = *session_opt;
        
        AccountInfo account;
        account.account_id = session.account_id;
        account.email = session.email;
        account.tenant_url = session.tenant_url;
        account.access_token = session.access_token;
        account.refresh_token = session.refresh_token;
        account.token_type = session.token_type;
        account.expires_at = session.expires_at;
        account.scope = session.scope;
        account.last_used = session.last_used;
        account.is_expired = session.is_expired();
        
        return account;
    });
}

std::future<bool> AugmentAuthManager::refresh_account_token(const std::string& account_id) {
    return std::async(std::launch::async, [this, account_id]() -> bool {
        if (!initialized_.load()) {
            throw AuthException("认证管理器未初始化");
        }
        
        // 获取账户会话
        auto session_future = session_store_->get_session(account_id);
        auto session_opt = session_future.get();
        
        if (!session_opt) {
            throw AuthException("账户不存在: " + account_id);
        }
        
        const auto& session = *session_opt;
        
        if (session.refresh_token.empty()) {
            throw AuthException("没有刷新token");
        }
        
        // 刷新token
        TokenRefreshRequest refresh_request;
        refresh_request.refresh_token = session.refresh_token;
        refresh_request.tenant_url = session.tenant_url;
        
        try {
            auto token_future = token_exchanger_->refresh_token(refresh_request);
            auto new_token_data = token_future.get();
            
            // 更新会话
            json updates;
            updates["access_token"] = new_token_data.access_token;
            if (!new_token_data.refresh_token.empty()) {
                updates["refresh_token"] = new_token_data.refresh_token;
            }
            if (new_token_data.expires_at) {
                updates["expires_at"] = std::chrono::duration_cast<std::chrono::seconds>(
                    new_token_data.expires_at->time_since_epoch()).count();
            }
            
            auto update_future = session_store_->update_session(account_id, updates);
            update_future.get();
            
            log_info("Token刷新成功: " + account_id);
            return true;
            
        } catch (const std::exception& e) {
            log_error("Token刷新失败: " + std::string(e.what()));
            return false;
        }
    });
}

std::future<void> AugmentAuthManager::remove_account(const std::string& account_id) {
    return std::async(std::launch::async, [this, account_id]() {
        if (!initialized_.load()) {
            throw AuthException("认证管理器未初始化");
        }
        
        // 检查是否是当前账户
        auto current_future = session_store_->get_current_account();
        auto current_account_id = current_future.get();
        
        if (current_account_id && *current_account_id == account_id) {
            // 清除当前账户
            auto clear_future = session_store_->set_current_account("");
            clear_future.get();
        }
        
        // 移除会话
        auto remove_future = session_store_->remove_session(account_id);
        remove_future.get();
        
        log_info("账户已移除: " + account_id);
    });
}

std::future<std::string> AugmentAuthManager::get_current_access_token() {
    return std::async(std::launch::async, [this]() -> std::string {
        auto current_future = get_current_account();
        auto current_account = current_future.get();
        
        if (!current_account) {
            throw AuthException("没有当前账户");
        }
        
        if (current_account->is_expired) {
            // 尝试刷新token
            auto refresh_future = refresh_account_token(current_account->account_id);
            bool refreshed = refresh_future.get();
            
            if (!refreshed) {
                throw AuthException("Token已过期且刷新失败");
            }
            
            // 重新获取账户信息
            auto updated_future = get_current_account();
            auto updated_account = updated_future.get();
            
            if (!updated_account) {
                throw AuthException("刷新后无法获取账户信息");
            }
            
            return updated_account->access_token;
        }
        
        return current_account->access_token;
    });
}

bool AugmentAuthManager::is_token_expired(
    const std::optional<std::chrono::system_clock::time_point>& expires_at) noexcept {
    
    if (!expires_at) {
        return false;
    }
    
    const auto now = std::chrono::system_clock::now();
    return now >= *expires_at;
}

void AugmentAuthManager::cleanup_pending_authorizations() {
    std::lock_guard<std::mutex> lock(pending_auth_mutex_);
    
    const auto now = std::chrono::system_clock::now();
    const auto timeout_threshold = now - config_.pending_auth_timeout;
    
    auto it = pending_authorizations_.begin();
    while (it != pending_authorizations_.end()) {
        if (it->second.created_at < timeout_threshold) {
            log_debug("清理过期的认证流程: " + it->first);
            it = pending_authorizations_.erase(it);
        } else {
            ++it;
        }
    }
}

// 私有方法实现
std::future<UserInfo> AugmentAuthManager::get_user_info(const std::string& access_token, 
                                                        const std::string& tenant_url) {
    return std::async(std::launch::async, [this, access_token, tenant_url]() -> UserInfo {
        // 尝试从JWT token中提取邮箱
        auto email_opt = extract_email_from_token(access_token);
        
        UserInfo user_info;
        if (email_opt) {
            user_info.email = *email_opt;
            user_info.name = *email_opt; // 使用邮箱作为名称
        } else {
            // 如果无法从token提取，使用默认值
            user_info.email = "user@" + tenant_url;
            user_info.name = "User";
        }
        
        return user_info;
    });
}

std::optional<std::string> AugmentAuthManager::extract_email_from_token(const std::string& token) const {
    // 简化的JWT解析（实际应用中应使用专门的JWT库）
    try {
        // JWT格式: header.payload.signature
        const auto first_dot = token.find('.');
        const auto second_dot = token.find('.', first_dot + 1);
        
        if (first_dot == std::string::npos || second_dot == std::string::npos) {
            return std::nullopt;
        }
        
        // 提取payload部分
        const std::string payload_b64 = token.substr(first_dot + 1, second_dot - first_dot - 1);
        
        // 简化的Base64解码（仅用于演示）
        // 实际应用中应使用正确的Base64解码
        // 这里暂时返回空，实际项目中需要实现JWT解析
        return std::nullopt;
        
    } catch (const std::exception&) {
        return std::nullopt;
    }
}

std::string AugmentAuthManager::generate_account_id(const UserInfo& user_info) const {
    return user_info.email; // 使用邮箱作为账户ID
}

bool AugmentAuthManager::validate_auth_state(const std::string& state) const {
    std::lock_guard<std::mutex> lock(pending_auth_mutex_);
    return pending_authorizations_.find(state) != pending_authorizations_.end();
}

void AugmentAuthManager::log_debug(const std::string& message) const {
    if (config_.debug) {
        std::cout << "[DEBUG] AugmentAuthManager: " << message << std::endl;
    }
}

void AugmentAuthManager::log_info(const std::string& message) const {
    std::cout << "[INFO] AugmentAuthManager: " << message << std::endl;
}

void AugmentAuthManager::log_error(const std::string& message) const {
    std::cerr << "[ERROR] AugmentAuthManager: " << message << std::endl;
}
