/**
 * @file token_exchanger.hpp
 * @brief Token交换器 - C++17实现
 * <AUTHOR> Agent
 * @date 2025-01-05
 * 
 * 现代C++实现的OAuth token交换器
 * 负责授权码到访问token的交换和token刷新
 */

#pragma once

#include <string>
#include <string_view>
#include <optional>
#include <chrono>
#include <future>
#include <memory>

// 第三方库依赖
#ifdef HAVE_NLOHMANN_JSON
#include <nlohmann/json.hpp>
#else
#include "simple_json.hpp"
#endif
#include "http_client.hpp"

namespace augment {

/**
 * @brief Token交换异常类
 */
class TokenExchangeException : public std::runtime_error {
public:
    explicit TokenExchangeException(const std::string& message)
        : std::runtime_error("Token交换错误: " + message) {}
};

/**
 * @brief Token数据结构
 */
struct TokenData {
    std::string access_token;     ///< 访问token
    std::string refresh_token;    ///< 刷新token
    std::string token_type;       ///< Token类型 (通常为"Bearer")
    std::optional<std::chrono::system_clock::time_point> expires_at; ///< 过期时间
    std::string scope;            ///< 授权范围
    
    /**
     * @brief 检查token是否已过期
     * @return 如果已过期则返回true
     */
    [[nodiscard]] bool is_expired() const noexcept;
    
    /**
     * @brief 获取剩余有效时间
     * @return 剩余时间（秒）
     */
    [[nodiscard]] std::chrono::seconds time_until_expiry() const noexcept;
};

/**
 * @brief 授权码交换请求参数
 */
struct CodeExchangeRequest {
    std::string code;             ///< 授权码
    std::string code_verifier;    ///< PKCE code verifier
    std::string tenant_url;       ///< 租户URL
    std::string client_id = "augment-vscode-extension";
    std::string redirect_uri = "vscode://augment.vscode-augment/auth/result";
};

/**
 * @brief Token刷新请求参数
 */
struct TokenRefreshRequest {
    std::string refresh_token;    ///< 刷新token
    std::string tenant_url;       ///< 租户URL
    std::string client_id = "augment-vscode-extension";
    std::optional<std::string> scope; ///< 可选的授权范围
};

/**
 * @brief Token交换器配置
 */
struct TokenExchangerConfig {
    bool debug = false;
    std::optional<std::string> client_secret;
    std::chrono::seconds request_timeout{30};
    std::size_t max_retries = 3;
    std::chrono::milliseconds retry_delay{1000};
    
    // HTTP客户端配置
    struct HttpConfig {
        std::string user_agent = "Augment-Auth-Client/1.0";
        bool verify_ssl = true;
        std::chrono::seconds connect_timeout{10};
        std::chrono::seconds read_timeout{30};
    } http;
};

/**
 * @brief Token交换器类
 * 
 * 负责OAuth 2.0 token交换和刷新操作
 * 支持PKCE、自动重试、错误处理等功能
 */
class TokenExchanger {
public:
    /**
     * @brief 构造函数
     * @param config Token交换器配置
     */
    explicit TokenExchanger(const TokenExchangerConfig& config = {});
    
    /**
     * @brief 析构函数
     */
    ~TokenExchanger();
    
    // 禁用拷贝，允许移动
    TokenExchanger(const TokenExchanger&) = delete;
    TokenExchanger& operator=(const TokenExchanger&) = delete;
    TokenExchanger(TokenExchanger&&) = default;
    TokenExchanger& operator=(TokenExchanger&&) = default;
    
    /**
     * @brief 将授权码交换为访问token
     * @param request 交换请求参数
     * @return Token数据
     * @throws TokenExchangeException 交换失败时
     */
    [[nodiscard]] std::future<TokenData> exchange_code_for_token(const CodeExchangeRequest& request);
    
    /**
     * @brief 使用刷新token获取新的访问token
     * @param request 刷新请求参数
     * @return 新的Token数据
     * @throws TokenExchangeException 刷新失败时
     */
    [[nodiscard]] std::future<TokenData> refresh_token(const TokenRefreshRequest& request);
    
    /**
     * @brief 解析回调URL并提取参数
     * @param callback_url 回调URL
     * @return 解析的参数
     * @throws TokenExchangeException 解析失败时
     */
    [[nodiscard]] static nlohmann::json parse_callback_url(const std::string& callback_url);
    
    /**
     * @brief 验证state参数
     * @param received_state 接收到的state
     * @param expected_state 期望的state
     * @return 如果匹配则返回true
     */
    [[nodiscard]] static bool validate_state(const std::string& received_state, 
                                             const std::string& expected_state);
    
    /**
     * @brief 验证token响应
     * @param response_json 响应JSON
     * @return 如果响应有效则返回true
     */
    [[nodiscard]] static bool validate_token_response(const nlohmann::json& response_json);
    
    /**
     * @brief 获取token端点URL
     * @param tenant_url 租户URL
     * @return Token端点URL
     */
    [[nodiscard]] static std::string get_token_endpoint(const std::string& tenant_url);
    
    /**
     * @brief 获取当前配置
     * @return Token交换器配置
     */
    [[nodiscard]] const TokenExchangerConfig& get_config() const noexcept { return config_; }
    
    /**
     * @brief 更新配置
     * @param config 新的配置
     */
    void set_config(const TokenExchangerConfig& config) { config_ = config; }

private:
    /**
     * @brief 执行HTTP POST请求
     * @param url 请求URL
     * @param form_data 表单数据
     * @param headers 请求头
     * @return 响应JSON
     * @throws TokenExchangeException 请求失败时
     */
    [[nodiscard]] std::future<nlohmann::json> make_http_request(
        const std::string& url,
        const std::unordered_map<std::string, std::string>& form_data,
        const std::unordered_map<std::string, std::string>& headers = {});
    
    /**
     * @brief 构建表单数据字符串
     * @param form_data 表单数据映射
     * @return URL编码的表单字符串
     */
    [[nodiscard]] static std::string build_form_data(
        const std::unordered_map<std::string, std::string>& form_data);
    
    /**
     * @brief 解析token响应
     * @param response_json 响应JSON
     * @return Token数据
     * @throws TokenExchangeException 解析失败时
     */
    [[nodiscard]] static TokenData parse_token_response(const nlohmann::json& response_json);
    
    /**
     * @brief 计算token过期时间
     * @param expires_in 过期秒数
     * @return 过期时间点
     */
    [[nodiscard]] static std::chrono::system_clock::time_point 
    calculate_expiry_time(int expires_in);
    
    /**
     * @brief 重试执行函数
     * @param func 要执行的函数
     * @param max_retries 最大重试次数
     * @param delay 重试延迟
     * @return 函数执行结果
     */
    template<typename Func>
    [[nodiscard]] auto retry_with_backoff(Func&& func, 
                                          std::size_t max_retries, 
                                          std::chrono::milliseconds delay) -> decltype(func());
    
    /**
     * @brief 记录调试信息
     * @param message 调试消息
     */
    void log_debug(const std::string& message) const;
    
    /**
     * @brief 记录错误信息
     * @param message 错误消息
     */
    void log_error(const std::string& message) const;

private:
    TokenExchangerConfig config_;
    std::unique_ptr<HttpClient> http_client_;
};

/**
 * @brief Token交换器构建器类
 * 
 * 提供流式API来配置和创建TokenExchanger实例
 */
class TokenExchangerBuilder {
public:
    TokenExchangerBuilder() = default;
    
    TokenExchangerBuilder& debug(bool enable = true) {
        config_.debug = enable;
        return *this;
    }
    
    TokenExchangerBuilder& client_secret(const std::string& secret) {
        config_.client_secret = secret;
        return *this;
    }
    
    TokenExchangerBuilder& request_timeout(std::chrono::seconds timeout) {
        config_.request_timeout = timeout;
        return *this;
    }
    
    TokenExchangerBuilder& max_retries(std::size_t retries) {
        config_.max_retries = retries;
        return *this;
    }
    
    TokenExchangerBuilder& retry_delay(std::chrono::milliseconds delay) {
        config_.retry_delay = delay;
        return *this;
    }
    
    TokenExchangerBuilder& user_agent(const std::string& agent) {
        config_.http.user_agent = agent;
        return *this;
    }
    
    TokenExchangerBuilder& verify_ssl(bool verify = true) {
        config_.http.verify_ssl = verify;
        return *this;
    }
    
    [[nodiscard]] std::unique_ptr<TokenExchanger> build() {
        return std::make_unique<TokenExchanger>(config_);
    }

private:
    TokenExchangerConfig config_;
};

} // namespace augment
