# Augment认证服务器 - 综合测试套件
# 版本: 1.0.0
# 日期: 2025-01-05
# 描述: 完整的功能测试、API验证、Web界面测试脚本

param(
    [string]$ServerHost = "localhost",
    [int]$ServerPort = 8080,
    [string]$ServerPath = "Release\augment_auth_server.exe",
    [switch]$SkipBuild,
    [switch]$SkipServerStart,
    [switch]$Verbose,
    [switch]$StopOnError
)

# 全局变量
$Global:TestResults = @()
$Global:ServerProcess = $null
$Global:BaseUrl = "http://${ServerHost}:${ServerPort}"
$Global:TestStartTime = Get-Date

# 颜色输出函数
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    switch ($Color) {
        "Red" { Write-Host $Message -ForegroundColor Red }
        "Green" { Write-Host $Message -ForegroundColor Green }
        "Yellow" { Write-Host $Message -ForegroundColor Yellow }
        "Blue" { Write-Host $Message -ForegroundColor Blue }
        "Cyan" { Write-Host $Message -ForegroundColor Cyan }
        "Magenta" { Write-Host $Message -ForegroundColor Magenta }
        default { Write-Host $Message }
    }
}

# 测试结果记录函数
function Add-TestResult {
    param(
        [string]$TestName,
        [bool]$Passed,
        [string]$Details = "",
        [double]$Duration = 0
    )
    
    $result = @{
        TestName = $TestName
        Passed = $Passed
        Details = $Details
        Duration = $Duration
        Timestamp = Get-Date
    }
    
    $Global:TestResults += $result
    
    $status = if ($Passed) { "✅ PASS" } else { "❌ FAIL" }
    $color = if ($Passed) { "Green" } else { "Red" }
    
    Write-ColorOutput "[$status] $TestName" $color
    if ($Details) {
        Write-ColorOutput "    Details: $Details" "Gray"
    }
    if ($Duration -gt 0) {
        Write-ColorOutput "    Duration: $($Duration.ToString('F2'))ms" "Gray"
    }
    
    if (-not $Passed -and $StopOnError) {
        Write-ColorOutput "❌ 测试失败，停止执行 (StopOnError模式)" "Red"
        Stop-TestServer
        exit 1
    }
}

# HTTP请求函数
function Invoke-HttpRequest {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = "",
        [int]$TimeoutSeconds = 30
    )
    
    try {
        $startTime = Get-Date
        
        if ($Method -eq "GET" -and $Body) {
            # 对于GET请求，将Body作为查询参数
            $separator = if ($Url.Contains("?")) { "&" } else { "?" }
            $Url += $separator + $Body
            $Body = ""
        }
        
        $response = if ($Body) {
            Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers -Body $Body -TimeoutSec $TimeoutSeconds
        } else {
            Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers -TimeoutSec $TimeoutSeconds
        }
        
        $duration = (Get-Date) - $startTime
        
        return @{
            Success = $true
            Data = $response
            Duration = $duration.TotalMilliseconds
            StatusCode = 200
        }
    }
    catch {
        $duration = (Get-Date) - $startTime
        return @{
            Success = $false
            Error = $_.Exception.Message
            Duration = $duration.TotalMilliseconds
            StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode } else { 0 }
        }
    }
}

# 服务器启动函数
function Start-TestServer {
    if ($SkipServerStart) {
        Write-ColorOutput "⏭️  跳过服务器启动" "Yellow"
        return $true
    }
    
    Write-ColorOutput "🚀 启动测试服务器..." "Blue"
    
    # 检查可执行文件是否存在
    if (-not (Test-Path $ServerPath)) {
        Write-ColorOutput "❌ 找不到服务器可执行文件: $ServerPath" "Red"
        return $false
    }
    
    # 检查端口是否被占用
    $portInUse = Get-NetTCPConnection -LocalPort $ServerPort -ErrorAction SilentlyContinue
    if ($portInUse) {
        Write-ColorOutput "⚠️  端口 $ServerPort 已被占用，尝试终止占用进程..." "Yellow"
        $processes = Get-Process | Where-Object { $_.ProcessName -like "*augment*" }
        foreach ($proc in $processes) {
            try {
                Stop-Process -Id $proc.Id -Force
                Write-ColorOutput "✅ 已终止进程: $($proc.ProcessName) (PID: $($proc.Id))" "Green"
            }
            catch {
                Write-ColorOutput "⚠️  无法终止进程: $($proc.ProcessName)" "Yellow"
            }
        }
        Start-Sleep -Seconds 2
    }
    
    # 启动服务器
    try {
        $Global:ServerProcess = Start-Process -FilePath $ServerPath -ArgumentList "--port", $ServerPort, "--debug" -PassThru -WindowStyle Hidden
        Write-ColorOutput "✅ 服务器进程已启动 (PID: $($Global:ServerProcess.Id))" "Green"
        
        # 等待服务器启动
        Write-ColorOutput "⏳ 等待服务器启动..." "Yellow"
        $maxWaitTime = 30
        $waitTime = 0
        
        do {
            Start-Sleep -Seconds 1
            $waitTime++
            try {
                $response = Invoke-WebRequest -Uri "$Global:BaseUrl/api/accounts" -TimeoutSec 5 -ErrorAction Stop
                Write-ColorOutput "✅ 服务器已就绪" "Green"
                return $true
            }
            catch {
                if ($Verbose) {
                    Write-ColorOutput "    等待中... ($waitTime/$maxWaitTime)" "Gray"
                }
            }
        } while ($waitTime -lt $maxWaitTime)
        
        Write-ColorOutput "❌ 服务器启动超时" "Red"
        return $false
    }
    catch {
        Write-ColorOutput "❌ 启动服务器失败: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 服务器停止函数
function Stop-TestServer {
    if ($Global:ServerProcess -and -not $Global:ServerProcess.HasExited) {
        Write-ColorOutput "🛑 停止测试服务器..." "Blue"
        try {
            Stop-Process -Id $Global:ServerProcess.Id -Force
            Write-ColorOutput "✅ 服务器已停止" "Green"
        }
        catch {
            Write-ColorOutput "⚠️  停止服务器时出现错误: $($_.Exception.Message)" "Yellow"
        }
    }
}

# 基础连接测试
function Test-BasicConnectivity {
    Write-ColorOutput "`n🔍 执行基础连接测试..." "Blue"
    
    # 测试1: 服务器响应
    $result = Invoke-HttpRequest -Url "$Global:BaseUrl/api/accounts"
    Add-TestResult -TestName "服务器基础连接" -Passed $result.Success -Details $result.Error -Duration $result.Duration
    
    # 测试2: Web界面访问
    $result = Invoke-HttpRequest -Url "$Global:BaseUrl/"
    $webAccessible = $result.Success -and ($result.Data -like "*Augment*" -or $result.Data -like "*认证*")
    Add-TestResult -TestName "Web界面访问" -Passed $webAccessible -Details "响应长度: $($result.Data.Length)" -Duration $result.Duration
    
    # 测试3: CORS头检查
    try {
        $headers = @{ "Origin" = "http://localhost:3000" }
        $response = Invoke-WebRequest -Uri "$Global:BaseUrl/api/accounts" -Headers $headers -Method GET
        $hasCors = $response.Headers["Access-Control-Allow-Origin"] -ne $null
        Add-TestResult -TestName "CORS支持检查" -Passed $hasCors -Details "CORS头存在: $hasCors"
    }
    catch {
        Add-TestResult -TestName "CORS支持检查" -Passed $false -Details $_.Exception.Message
    }
}

# API端点测试
function Test-ApiEndpoints {
    Write-ColorOutput "`n🔌 执行API端点测试..." "Blue"
    
    # 测试所有GET端点
    $endpoints = @(
        @{ Path = "/api/accounts"; Name = "获取账户列表" },
        @{ Path = "/api/current-account"; Name = "获取当前账户" },
        @{ Path = "/api/start-auth"; Name = "开始认证流程" }
    )
    
    foreach ($endpoint in $endpoints) {
        $result = Invoke-HttpRequest -Url "$Global:BaseUrl$($endpoint.Path)"
        $isValidJson = $false
        
        if ($result.Success) {
            try {
                $jsonData = $result.Data | ConvertTo-Json -Depth 10
                $isValidJson = $true
            }
            catch {
                $isValidJson = $false
            }
        }
        
        Add-TestResult -TestName $endpoint.Name -Passed ($result.Success -and $isValidJson) -Details "状态码: $($result.StatusCode)" -Duration $result.Duration
    }
    
    # 测试带参数的端点
    $paramEndpoints = @(
        @{ Path = "/api/complete-auth"; Params = "callbackUrl=test://callback"; Name = "完成认证流程" },
        @{ Path = "/api/switch-account"; Params = "accountId=<EMAIL>"; Name = "切换账户" },
        @{ Path = "/api/refresh-token"; Params = "accountId=<EMAIL>"; Name = "刷新Token" }
    )
    
    foreach ($endpoint in $paramEndpoints) {
        $result = Invoke-HttpRequest -Url "$Global:BaseUrl$($endpoint.Path)" -Body $endpoint.Params
        Add-TestResult -TestName $endpoint.Name -Passed $result.Success -Details "状态码: $($result.StatusCode)" -Duration $result.Duration
    }
}

# 认证流程测试
function Test-AuthenticationFlow {
    Write-ColorOutput "`n🔐 执行认证流程测试..." "Blue"
    
    # 测试1: 开始认证流程
    $startAuthResult = Invoke-HttpRequest -Url "$Global:BaseUrl/api/start-auth"
    $hasRequiredFields = $false
    
    if ($startAuthResult.Success) {
        $data = $startAuthResult.Data
        $hasRequiredFields = ($data.flowId -ne $null) -and ($data.authUrl -ne $null) -and ($data.state -ne $null)
    }
    
    Add-TestResult -TestName "认证流程初始化" -Passed ($startAuthResult.Success -and $hasRequiredFields) -Details "包含必需字段: $hasRequiredFields" -Duration $startAuthResult.Duration
    
    # 测试2: OAuth回调处理
    $callbackUrl = "$Global:BaseUrl/callback?code=test_code&state=test_state"
    $callbackResult = Invoke-HttpRequest -Url $callbackUrl
    Add-TestResult -TestName "OAuth回调处理" -Passed $callbackResult.Success -Details "状态码: $($callbackResult.StatusCode)" -Duration $callbackResult.Duration
    
    # 测试3: 无效参数处理
    $invalidResult = Invoke-HttpRequest -Url "$Global:BaseUrl/api/complete-auth" -Body "callbackUrl=invalid_url"
    $handlesInvalidInput = $invalidResult.Success -or ($invalidResult.StatusCode -ge 400 -and $invalidResult.StatusCode -lt 500)
    Add-TestResult -TestName "无效参数处理" -Passed $handlesInvalidInput -Details "正确处理无效输入" -Duration $invalidResult.Duration
}

# 性能测试
function Test-Performance {
    Write-ColorOutput "`n⚡ 执行性能测试..." "Blue"
    
    # 并发请求测试
    $concurrentRequests = 10
    $jobs = @()
    
    Write-ColorOutput "    执行 $concurrentRequests 个并发请求..." "Gray"
    
    for ($i = 1; $i -le $concurrentRequests; $i++) {
        $job = Start-Job -ScriptBlock {
            param($BaseUrl)
            try {
                $response = Invoke-RestMethod -Uri "$BaseUrl/api/accounts" -TimeoutSec 10
                return @{ Success = $true; Duration = (Measure-Command { Invoke-RestMethod -Uri "$BaseUrl/api/accounts" }).TotalMilliseconds }
            }
            catch {
                return @{ Success = $false; Error = $_.Exception.Message }
            }
        } -ArgumentList $Global:BaseUrl
        
        $jobs += $job
    }
    
    # 等待所有任务完成
    $results = $jobs | Wait-Job | Receive-Job
    $jobs | Remove-Job
    
    $successCount = ($results | Where-Object { $_.Success }).Count
    $avgDuration = ($results | Where-Object { $_.Success } | Measure-Object -Property Duration -Average).Average
    
    $performancePass = ($successCount -ge ($concurrentRequests * 0.8)) -and ($avgDuration -lt 1000)
    Add-TestResult -TestName "并发性能测试" -Passed $performancePass -Details "成功率: $successCount/$concurrentRequests, 平均响应时间: $($avgDuration.ToString('F2'))ms"
    
    # 内存使用测试
    if ($Global:ServerProcess -and -not $Global:ServerProcess.HasExited) {
        $Global:ServerProcess.Refresh()
        $memoryUsage = $Global:ServerProcess.WorkingSet64 / 1MB
        $memoryPass = $memoryUsage -lt 200  # 小于200MB
        Add-TestResult -TestName "内存使用测试" -Passed $memoryPass -Details "内存使用: $($memoryUsage.ToString('F2'))MB"
    }
}

# 错误处理测试
function Test-ErrorHandling {
    Write-ColorOutput "`n🛡️  执行错误处理测试..." "Blue"
    
    # 测试不存在的端点
    $result404 = Invoke-HttpRequest -Url "$Global:BaseUrl/api/nonexistent"
    $handles404 = $result404.StatusCode -eq 404 -or (-not $result404.Success)
    Add-TestResult -TestName "404错误处理" -Passed $handles404 -Details "状态码: $($result404.StatusCode)"
    
    # 测试恶意输入
    $maliciousInputs = @(
        "callbackUrl=javascript:alert('xss')",
        "accountId='; DROP TABLE users; --",
        "callbackUrl=" + ("A" * 10000)  # 超长输入
    )
    
    foreach ($input in $maliciousInputs) {
        $result = Invoke-HttpRequest -Url "$Global:BaseUrl/api/complete-auth" -Body $input
        $handledSafely = (-not $result.Success) -or ($result.Data -notlike "*error*" -and $result.Data -notlike "*exception*")
        Add-TestResult -TestName "恶意输入防护" -Passed $handledSafely -Details "输入长度: $($input.Length)"
    }
}

# 生成测试报告
function Generate-TestReport {
    Write-ColorOutput "`n📊 生成测试报告..." "Blue"
    
    $totalTests = $Global:TestResults.Count
    $passedTests = ($Global:TestResults | Where-Object { $_.Passed }).Count
    $failedTests = $totalTests - $passedTests
    $successRate = if ($totalTests -gt 0) { ($passedTests / $totalTests) * 100 } else { 0 }
    $totalDuration = (Get-Date) - $Global:TestStartTime
    
    $report = @"
# Augment认证服务器测试报告
生成时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
测试持续时间: $($totalDuration.ToString('hh\:mm\:ss'))

## 测试概览
- 总测试数: $totalTests
- 通过测试: $passedTests
- 失败测试: $failedTests
- 成功率: $($successRate.ToString('F1'))%

## 测试结果详情
"@

    foreach ($result in $Global:TestResults) {
        $status = if ($result.Passed) { "✅ PASS" } else { "❌ FAIL" }
        $report += "`n- [$status] $($result.TestName)"
        if ($result.Details) {
            $report += " - $($result.Details)"
        }
        if ($result.Duration -gt 0) {
            $report += " ($($result.Duration.ToString('F2'))ms)"
        }
    }
    
    $report += "`n`n## 系统信息"
    $report += "`n- 服务器地址: $Global:BaseUrl"
    $report += "`n- 操作系统: $($env:OS)"
    $report += "`n- PowerShell版本: $($PSVersionTable.PSVersion)"
    
    # 保存报告到文件
    $reportFile = "test_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
    $report | Out-File -FilePath $reportFile -Encoding UTF8
    
    Write-ColorOutput "`n📋 测试报告已保存到: $reportFile" "Green"
    
    # 显示摘要
    Write-ColorOutput "`n📈 测试摘要:" "Cyan"
    Write-ColorOutput "   总测试数: $totalTests" "White"
    Write-ColorOutput "   通过: $passedTests" "Green"
    Write-ColorOutput "   失败: $failedTests" "Red"
    Write-ColorOutput "   成功率: $($successRate.ToString('F1'))%" $(if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 70) { "Yellow" } else { "Red" })
    Write-ColorOutput "   总耗时: $($totalDuration.ToString('hh\:mm\:ss'))" "White"
}

# 主测试流程
function Main {
    Write-ColorOutput "🧪 Augment认证服务器 - 综合测试套件" "Cyan"
    Write-ColorOutput "================================================" "Cyan"
    Write-ColorOutput "测试目标: $Global:BaseUrl" "White"
    Write-ColorOutput "开始时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "White"
    Write-ColorOutput "================================================" "Cyan"
    
    try {
        # 构建项目（如果需要）
        if (-not $SkipBuild) {
            Write-ColorOutput "`n🔨 构建项目..." "Blue"
            $buildResult = & cmake --build . --config Release
            if ($LASTEXITCODE -ne 0) {
                Write-ColorOutput "❌ 构建失败" "Red"
                return
            }
            Write-ColorOutput "✅ 构建成功" "Green"
        }
        
        # 启动服务器
        if (-not (Start-TestServer)) {
            Write-ColorOutput "❌ 无法启动服务器，测试终止" "Red"
            return
        }
        
        # 执行测试套件
        Test-BasicConnectivity
        Test-ApiEndpoints
        Test-AuthenticationFlow
        Test-Performance
        Test-ErrorHandling
        
        # 生成报告
        Generate-TestReport
        
    }
    finally {
        # 清理资源
        Stop-TestServer
    }
}

# 脚本入口点
if ($MyInvocation.InvocationName -ne '.') {
    Main
}
