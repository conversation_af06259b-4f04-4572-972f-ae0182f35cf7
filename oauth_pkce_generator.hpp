/**
 * @file oauth_pkce_generator.hpp
 * @brief OAuth PKCE生成器 - C++17实现
 * <AUTHOR> Agent
 * @date 2025-01-05
 * 
 * 现代C++实现的OAuth 2.0 PKCE (Proof Key for Code Exchange) 生成器
 * 符合RFC 7636标准，提供安全的授权码流程
 */

#pragma once

#include <string>
#include <string_view>
#include <optional>
#include <unordered_map>
#include <chrono>
#include <memory>

// 第三方库依赖
#ifdef HAVE_NLOHMANN_JSON
#include <nlohmann/json.hpp>
#else
#include "simple_json.hpp"
#endif

namespace augment {

/**
 * @brief PKCE生成异常类
 */
class PKCEException : public std::runtime_error {
public:
    explicit PKCEException(const std::string& message)
        : std::runtime_error("PKCE错误: " + message) {}
};

/**
 * @brief PKCE参数结构
 */
struct PKCEParams {
    std::string code_verifier;    ///< Code verifier (128字符)
    std::string code_challenge;   ///< Code challenge (43字符)
    std::string method = "S256";  ///< Challenge方法
};

/**
 * @brief OAuth授权URL数据结构
 */
struct AuthorizationURLData {
    std::string url;              ///< 完整的授权URL
    std::string code_verifier;    ///< Code verifier (安全存储)
    std::string state;            ///< State参数 (CSRF保护)
    std::string code_challenge;   ///< Code challenge
};

/**
 * @brief 回调URL解析结果
 */
struct CallbackData {
    std::string code;                    ///< 授权码
    std::string state;                   ///< State参数
    std::string tenant_url;              ///< 租户URL
    std::optional<std::string> error;    ///< 错误码
    std::optional<std::string> error_description; ///< 错误描述
};

/**
 * @brief OAuth配置结构
 */
struct OAuthConfig {
    std::string auth_base_url = "https://auth.augmentcode.com/authorize";
    std::string client_id = "augment-vscode-extension";
    std::string redirect_uri = "vscode://augment.vscode-augment/auth/result";
    std::string scope = "email";
    std::string response_type = "code";
    std::string code_challenge_method = "S256";
    std::string prompt = "login";
    
    // 自定义参数
    std::unordered_map<std::string, std::string> custom_params;
};

/**
 * @brief OAuth PKCE生成器类
 * 
 * 提供完整的OAuth 2.0 + PKCE流程支持
 * 包括授权URL生成、参数验证、回调解析等功能
 */
class OAuthPKCEGenerator {
public:
    /**
     * @brief 构造函数
     * @param config OAuth配置
     */
    explicit OAuthPKCEGenerator(const OAuthConfig& config = {});
    
    /**
     * @brief 析构函数
     */
    ~OAuthPKCEGenerator() = default;
    
    // 允许拷贝和移动
    OAuthPKCEGenerator(const OAuthPKCEGenerator&) = default;
    OAuthPKCEGenerator& operator=(const OAuthPKCEGenerator&) = default;
    OAuthPKCEGenerator(OAuthPKCEGenerator&&) = default;
    OAuthPKCEGenerator& operator=(OAuthPKCEGenerator&&) = default;
    
    /**
     * @brief 生成加密安全的随机字符串（用于code_verifier）
     * @param length 字符串长度 (43-128字符)
     * @return URL安全的随机字符串
     * @throws PKCEException 生成失败时
     */
    [[nodiscard]] static std::string generate_random_string(std::size_t length = 128);
    
    /**
     * @brief 将数据转换为Base64URL编码 (RFC 4648 Section 5)
     * @param data 要编码的数据
     * @return Base64URL编码字符串（无填充）
     */
    [[nodiscard]] static std::string base64url_encode(std::string_view data);
    
    /**
     * @brief 从code_verifier生成PKCE code_challenge（使用SHA256）
     * @param code_verifier code verifier字符串
     * @return Base64URL编码的SHA256哈希
     * @throws PKCEException 生成失败时
     */
    [[nodiscard]] static std::string generate_code_challenge(const std::string& code_verifier);
    
    /**
     * @brief 生成PKCE参数（code_verifier和code_challenge）
     * @return PKCE参数结构
     * @throws PKCEException 生成失败时
     */
    [[nodiscard]] static PKCEParams generate_pkce_params();
    
    /**
     * @brief 生成state参数（使用UUID v4）
     * @return UUID v4字符串
     * @throws PKCEException 生成失败时
     */
    [[nodiscard]] static std::string generate_state();
    
    /**
     * @brief 生成完整的OAuth授权URL（带PKCE）
     * @param custom_params 自定义参数覆盖
     * @return 授权URL数据
     * @throws PKCEException 生成失败时
     */
    [[nodiscard]] AuthorizationURLData generate_authorization_url(
        const std::unordered_map<std::string, std::string>& custom_params = {}) const;
    
    /**
     * @brief 验证生成的参数是否符合预期格式
     * @param code_challenge 生成的code challenge
     * @param state 生成的state
     * @return 如果参数有效则返回true
     */
    [[nodiscard]] static bool validate_parameters(const std::string& code_challenge, 
                                                  const std::string& state);
    
    /**
     * @brief 解析授权回调URL并提取参数
     * @param callback_url 回调URL
     * @return 解析的参数
     * @throws PKCEException 解析失败时
     */
    [[nodiscard]] static CallbackData parse_callback_url(const std::string& callback_url);
    
    /**
     * @brief 验证回调state参数
     * @param received_state 从回调URL接收的state
     * @param original_state 原始生成的state
     * @return state是否匹配
     */
    [[nodiscard]] static bool validate_state(const std::string& received_state, 
                                             const std::string& original_state);
    
    /**
     * @brief 获取当前配置
     * @return OAuth配置
     */
    [[nodiscard]] const OAuthConfig& get_config() const noexcept { return config_; }
    
    /**
     * @brief 更新配置
     * @param config 新的OAuth配置
     */
    void set_config(const OAuthConfig& config) { config_ = config; }
    
    /**
     * @brief 设置客户端ID
     * @param client_id 客户端标识符
     */
    void set_client_id(const std::string& client_id) { config_.client_id = client_id; }
    
    /**
     * @brief 设置重定向URI
     * @param redirect_uri 重定向URI
     */
    void set_redirect_uri(const std::string& redirect_uri) { config_.redirect_uri = redirect_uri; }
    
    /**
     * @brief 设置授权服务器URL
     * @param auth_url 授权服务器URL
     */
    void set_auth_base_url(const std::string& auth_url) { config_.auth_base_url = auth_url; }

private:
    /**
     * @brief 构建URL查询参数
     * @param params 参数映射
     * @return URL编码的查询字符串
     */
    [[nodiscard]] static std::string build_query_string(
        const std::unordered_map<std::string, std::string>& params);
    
    /**
     * @brief URL编码字符串
     * @param str 要编码的字符串
     * @return URL编码后的字符串
     */
    [[nodiscard]] static std::string url_encode(const std::string& str);
    
    /**
     * @brief URL解码字符串
     * @param str 要解码的字符串
     * @return URL解码后的字符串
     */
    [[nodiscard]] static std::string url_decode(const std::string& str);
    
    /**
     * @brief 解析URL查询参数
     * @param query_string 查询字符串
     * @return 参数映射
     */
    [[nodiscard]] static std::unordered_map<std::string, std::string> 
    parse_query_string(const std::string& query_string);
    
    /**
     * @brief 验证code_verifier格式
     * @param code_verifier 要验证的code verifier
     * @return 如果格式有效则返回true
     */
    [[nodiscard]] static bool is_valid_code_verifier(const std::string& code_verifier);
    
    /**
     * @brief 验证code_challenge格式
     * @param code_challenge 要验证的code challenge
     * @return 如果格式有效则返回true
     */
    [[nodiscard]] static bool is_valid_code_challenge(const std::string& code_challenge);
    
    /**
     * @brief 验证state格式
     * @param state 要验证的state
     * @return 如果格式有效则返回true
     */
    [[nodiscard]] static bool is_valid_state(const std::string& state);

private:
    OAuthConfig config_;
};

/**
 * @brief OAuth PKCE生成器构建器类
 * 
 * 提供流式API来配置和创建OAuthPKCEGenerator实例
 */
class OAuthPKCEGeneratorBuilder {
public:
    OAuthPKCEGeneratorBuilder() = default;
    
    OAuthPKCEGeneratorBuilder& auth_base_url(const std::string& url) {
        config_.auth_base_url = url;
        return *this;
    }
    
    OAuthPKCEGeneratorBuilder& client_id(const std::string& id) {
        config_.client_id = id;
        return *this;
    }
    
    OAuthPKCEGeneratorBuilder& redirect_uri(const std::string& uri) {
        config_.redirect_uri = uri;
        return *this;
    }
    
    OAuthPKCEGeneratorBuilder& scope(const std::string& scope) {
        config_.scope = scope;
        return *this;
    }
    
    OAuthPKCEGeneratorBuilder& prompt(const std::string& prompt) {
        config_.prompt = prompt;
        return *this;
    }
    
    OAuthPKCEGeneratorBuilder& custom_param(const std::string& key, const std::string& value) {
        config_.custom_params[key] = value;
        return *this;
    }
    
    [[nodiscard]] std::unique_ptr<OAuthPKCEGenerator> build() {
        return std::make_unique<OAuthPKCEGenerator>(config_);
    }

private:
    OAuthConfig config_;
};

/**
 * @brief 便利函数：快速生成授权URL
 * @param config 配置选项
 * @return 生成结果
 */
[[nodiscard]] AuthorizationURLData generate_oauth_url(const OAuthConfig& config = {});

} // namespace augment
