# 测试Web界面的简单脚本
Write-Host "测试Web界面..." -ForegroundColor Green

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -UseBasicParsing -TimeoutSec 5
    Write-Host "成功! 状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Content-Type: $($response.Headers['Content-Type'])" -ForegroundColor White
    Write-Host "内容预览: $($response.Content.Substring(0, [Math]::Min(100, $response.Content.Length)))" -ForegroundColor White
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "测试API端点..." -ForegroundColor Green

$endpoints = @("/api/accounts", "/api/current-account")
foreach ($endpoint in $endpoints) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080$endpoint" -UseBasicParsing -TimeoutSec 5
        Write-Host "✅ $endpoint - 状态码: $($response.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Host "❌ $endpoint - 错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Read-Host "按Enter继续"
