=== Augment OAuth PKCE实现（仅使用GET请求）===

步骤1：生成OAuth授权URL
========================
生成的PKCE参数:
  Code Verifier: oT8kXXTIyMcoPU4ptekE...
  Code Challenge: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAl28RM0WsTgo
  State: 8HfnPfh24JX
  Client ID: v

生成的URL:
https://auth.augmentcode.com/authorize?response_type=code&code_challenge=AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAl28RM0WsTgo&client_id=v&state=8HfnPfh24JX&prompt=login

真实例子:
https://auth.augmentcode.com/authorize?response_type=code&code_challenge=iEqlpeaO56AdT0tpw5Udfx40Nny0PBpkGJzUMKcUcvg&client_id=v&state=ZozI1N6judo&prompt=login

------------------------------------------------------------
步骤2：处理Token提取响应
========================
=== 处理Token提取响应 ===
JSON响应: {"code":"_374dc88ef229ce029e5e997430f0d2c8","state":"855K7No7Mro","tenant_url":"https://d10.api.augmentcode.com/"}

提取的数据:
  tenant_url: https://d10.api.augmentcode.com/
  state: 855K7No7Mro
  code: _374dc88ef229ce029e5e997430f0d2c8

✅ 所有必需字段都存在
准备进行token交换...
------------------------------------------------------------
步骤3：生成Token交换URL（GET请求）
==================================
=== 生成Token交换URL（GET请求）===
Token交换URL:
https://d10.api.augmentcode.com/oauth/token?grant_type=authorization_code&code=_374dc88ef229ce029e5e997430f0d2c8&code_verifier=dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk&client_id=v&redirect_uri=vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult

------------------------------------------------------------
步骤4：验证最终Token
==================
=== 验证最终Token格式 ===
Token: 3088021e04396f9bef344c866d4f7fdf417d545d3ff2417399e9b166ca7c4941
长度: 64 字符
✅ Token格式有效（64字符十六进制）

============================================================
✅ OAuth PKCE实现完成！
✅ 所有请求都使用GET方法
✅ URL格式匹配真实例子
✅ JSON解析工作正常
✅ Token验证通过
