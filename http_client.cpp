/**
 * @file http_client.cpp
 * @brief Simple HTTP client wrapper implementation
 * <AUTHOR> Agent
 * @date 2025-01-05
 */

#include "http_client.hpp"
#include <iostream>

namespace augment {

HttpClient::HttpClient(const std::string& base_url) 
    : base_url_(base_url) {
    // Extract host and port from base_url
    // For simplicity, assume format like "https://example.com" or "http://example.com:8080"
    std::string host;
    int port = 80;
    
    if (base_url_.find("https://") == 0) {
        host = base_url_.substr(8);
        port = 443;
    } else if (base_url_.find("http://") == 0) {
        host = base_url_.substr(7);
        port = 80;
    } else {
        host = base_url_;
    }
    
    // Remove path part if exists
    auto slash_pos = host.find('/');
    if (slash_pos != std::string::npos) {
        host = host.substr(0, slash_pos);
    }
    
    // Extract port if specified
    auto colon_pos = host.find(':');
    if (colon_pos != std::string::npos) {
        try {
            port = std::stoi(host.substr(colon_pos + 1));
            host = host.substr(0, colon_pos);
        } catch (...) {
            // Keep default port
        }
    }
    
    client_ = std::make_unique<httplib::Client>(host, port);
}

std::shared_ptr<httplib::Response> HttpClient::Post(const std::string& path,
                                                   const std::string& body,
                                                   const std::string& content_type) {
    if (!client_) {
        return nullptr;
    }
    
    return client_->Post(path, body, content_type);
}

std::shared_ptr<httplib::Response> HttpClient::Post(const std::string& path,
                                                   const httplib::Headers& headers,
                                                   const std::string& body,
                                                   const std::string& content_type) {
    if (!client_) {
        return nullptr;
    }
    
    return client_->Post(path, headers, body, content_type);
}

} // namespace augment
