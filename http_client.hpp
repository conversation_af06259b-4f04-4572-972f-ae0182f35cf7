/**
 * @file http_client.hpp
 * @brief Simple HTTP client wrapper
 * <AUTHOR> Agent
 * @date 2025-01-05
 */

#pragma once

#include <string>
#include <memory>
#include <chrono>
#include "httplib.h"

namespace augment {

/**
 * @brief Simple HTTP client wrapper class
 */
class HttpClient {
public:
    /**
     * @brief Constructor
     * @param base_url Base URL for the HTTP client
     */
    explicit HttpClient(const std::string& base_url);
    
    /**
     * @brief Destructor
     */
    ~HttpClient() = default;
    
    /**
     * @brief Set connection timeout
     * @param timeout Timeout duration
     */
    template<typename Rep, typename Period>
    void set_connection_timeout(const std::chrono::duration<Rep, Period>& timeout) {
        if (client_) {
            client_->set_connection_timeout(timeout);
        }
    }
    
    /**
     * @brief Set read timeout
     * @param timeout Timeout duration
     */
    template<typename Rep, typename Period>
    void set_read_timeout(const std::chrono::duration<Rep, Period>& timeout) {
        if (client_) {
            client_->set_read_timeout(timeout);
        }
    }
    
    /**
     * @brief Perform HTTP POST request
     * @param path Request path
     * @param body Request body
     * @param content_type Content type
     * @return Response pointer
     */
    std::shared_ptr<httplib::Response> Post(const std::string& path,
                                           const std::string& body,
                                           const std::string& content_type);
    
    /**
     * @brief Perform HTTP POST request with headers
     * @param path Request path
     * @param headers Request headers
     * @param body Request body
     * @param content_type Content type
     * @return Response pointer
     */
    std::shared_ptr<httplib::Response> Post(const std::string& path,
                                           const httplib::Headers& headers,
                                           const std::string& body,
                                           const std::string& content_type);

private:
    std::string base_url_;
    std::unique_ptr<httplib::Client> client_;
};

} // namespace augment
