/**
 * @file session_store.cpp
 * @brief 会话存储器实现
 */

#include "session_store.hpp"
#include <fstream>
#include <iostream>
#include <thread>

using namespace augment;
using json = nlohmann::json;

// SessionData 实现
bool SessionData::is_expired() const noexcept {
    if (!expires_at) {
        return false;
    }
    
    const auto now = std::chrono::system_clock::now();
    return now >= *expires_at;
}

void SessionData::update_last_used() noexcept {
    last_used = std::chrono::system_clock::now();
}

nlohmann::json SessionData::to_json() const {
    json j;
    j["account_id"] = account_id;
    j["email"] = email;
    j["tenant_url"] = tenant_url;
    j["access_token"] = access_token;
    j["refresh_token"] = refresh_token;
    j["token_type"] = token_type;
    j["scope"] = scope;
    
    // 转换时间点为时间戳
    j["created_at"] = std::chrono::duration_cast<std::chrono::seconds>(
        created_at.time_since_epoch()).count();
    j["last_used"] = std::chrono::duration_cast<std::chrono::seconds>(
        last_used.time_since_epoch()).count();
    
    if (expires_at) {
        j["expires_at"] = std::chrono::duration_cast<std::chrono::seconds>(
            expires_at->time_since_epoch()).count();
    }
    
    return j;
}

SessionData SessionData::from_json(const nlohmann::json& j) {
    SessionData session;
    
    session.account_id = j.value("account_id", "");
    session.email = j.value("email", "");
    session.tenant_url = j.value("tenant_url", "");
    session.access_token = j.value("access_token", "");
    session.refresh_token = j.value("refresh_token", "");
    session.token_type = j.value("token_type", "Bearer");
    session.scope = j.value("scope", "");
    
    // 转换时间戳为时间点
    if (j.contains("created_at")) {
        const auto timestamp = j["created_at"].get<int64_t>();
        session.created_at = std::chrono::system_clock::from_time_t(timestamp);
    } else {
        session.created_at = std::chrono::system_clock::now();
    }
    
    if (j.contains("last_used")) {
        const auto timestamp = j["last_used"].get<int64_t>();
        session.last_used = std::chrono::system_clock::from_time_t(timestamp);
    } else {
        session.last_used = std::chrono::system_clock::now();
    }
    
    if (j.contains("expires_at")) {
        const auto timestamp = j["expires_at"].get<int64_t>();
        session.expires_at = std::chrono::system_clock::from_time_t(timestamp);
    }
    
    return session;
}

// SessionStore 实现
SessionStore::SessionStore(const SessionStoreConfig& config)
    : config_(config), storage_path_(config.storage_path) {
}

SessionStore::~SessionStore() {
    stop_auto_cleanup();
}

std::future<void> SessionStore::initialize() {
    return std::async(std::launch::async, [this]() {
        std::lock_guard<std::mutex> lock(storage_mutex_);
        
        if (initialized_.load()) {
            return;
        }
        
        // 确保存储目录存在
        ensure_storage_directory();
        
        // 生成加密密钥（如果需要）
        if (config_.encrypt_tokens && config_.encryption_key.empty()) {
            encryption_key_ = generate_encryption_key();
        } else {
            encryption_key_ = config_.encryption_key;
        }
        
        // 启动自动清理（如果启用）
        if (config_.auto_cleanup) {
            start_auto_cleanup();
        }
        
        initialized_.store(true);
    });
}

std::future<void> SessionStore::store_session(const SessionData& session) {
    return std::async(std::launch::async, [this, session]() {
        if (!initialized_.load()) {
            throw SessionStoreException("会话存储未初始化");
        }
        
        std::lock_guard<std::mutex> lock(storage_mutex_);
        
        // 验证账户ID
        if (!is_valid_account_id(session.account_id)) {
            throw SessionStoreException("无效的账户ID");
        }
        
        // 序列化会话数据
        const json session_json = session.to_json();
        std::string content = session_json.dump(2);
        
        // 加密（如果启用）
        if (config_.encrypt_tokens) {
            content = encrypt_data(content);
        }
        
        // 写入文件
        const auto file_path = get_session_file_path(session.account_id);
        write_file(file_path, content);
    });
}

std::future<std::optional<SessionData>> SessionStore::get_session(const std::string& account_id) {
    return std::async(std::launch::async, [this, account_id]() -> std::optional<SessionData> {
        if (!initialized_.load()) {
            throw SessionStoreException("会话存储未初始化");
        }
        
        std::lock_guard<std::mutex> lock(storage_mutex_);
        
        // 验证账户ID
        if (!is_valid_account_id(account_id)) {
            return std::nullopt;
        }
        
        const auto file_path = get_session_file_path(account_id);
        
        // 检查文件是否存在
        if (!std::filesystem::exists(file_path)) {
            return std::nullopt;
        }
        
        try {
            // 读取文件
            std::string content = read_file(file_path);
            
            // 解密（如果启用）
            if (config_.encrypt_tokens) {
                content = decrypt_data(content);
            }
            
            // 解析JSON
            const json session_json = json::parse(content);
            return SessionData::from_json(session_json);
            
        } catch (const std::exception& e) {
            throw SessionStoreException("读取会话失败: " + std::string(e.what()));
        }
    });
}

std::future<void> SessionStore::update_session(const std::string& account_id, 
                                              const nlohmann::json& updates) {
    return std::async(std::launch::async, [this, account_id, updates]() {
        // 获取现有会话
        auto session_future = get_session(account_id);
        auto session_opt = session_future.get();
        
        if (!session_opt) {
            throw SessionStoreException("会话不存在: " + account_id);
        }
        
        auto session = *session_opt;
        
        // 应用更新
        if (updates.contains("access_token")) {
            session.access_token = updates["access_token"];
        }
        if (updates.contains("refresh_token")) {
            session.refresh_token = updates["refresh_token"];
        }
        if (updates.contains("expires_at")) {
            const auto timestamp = updates["expires_at"].get<int64_t>();
            session.expires_at = std::chrono::system_clock::from_time_t(timestamp);
        }
        
        // 更新最后使用时间
        session.update_last_used();
        
        // 存储更新后的会话
        auto store_future = store_session(session);
        store_future.get();
    });
}

std::future<void> SessionStore::remove_session(const std::string& account_id) {
    return std::async(std::launch::async, [this, account_id]() {
        if (!initialized_.load()) {
            throw SessionStoreException("会话存储未初始化");
        }
        
        std::lock_guard<std::mutex> lock(storage_mutex_);
        
        const auto file_path = get_session_file_path(account_id);
        
        if (std::filesystem::exists(file_path)) {
            secure_delete_file(file_path);
        }
    });
}

std::future<std::vector<SessionSummary>> SessionStore::list_sessions() {
    return std::async(std::launch::async, [this]() -> std::vector<SessionSummary> {
        if (!initialized_.load()) {
            throw SessionStoreException("会话存储未初始化");
        }
        
        std::vector<SessionSummary> summaries;
        std::lock_guard<std::mutex> lock(storage_mutex_);
        
        // 获取当前账户
        auto current_future = get_current_account();
        auto current_account = current_future.get();
        
        // 遍历存储目录
        for (const auto& entry : std::filesystem::directory_iterator(storage_path_)) {
            if (entry.is_regular_file() && entry.path().extension() == ".json") {
                try {
                    const std::string account_id = entry.path().stem().string();
                    
                    auto session_future = get_session(account_id);
                    auto session_opt = session_future.get();
                    
                    if (session_opt) {
                        const auto& session = *session_opt;
                        
                        SessionSummary summary;
                        summary.account_id = session.account_id;
                        summary.email = session.email;
                        summary.tenant_url = session.tenant_url;
                        summary.last_used = session.last_used;
                        summary.is_expired = session.is_expired();
                        summary.is_current = (current_account && *current_account == account_id);
                        
                        summaries.push_back(summary);
                    }
                } catch (const std::exception&) {
                    // 忽略损坏的会话文件
                    continue;
                }
            }
        }
        
        return summaries;
    });
}

std::future<void> SessionStore::set_current_account(const std::string& account_id) {
    return std::async(std::launch::async, [this, account_id]() {
        if (!initialized_.load()) {
            throw SessionStoreException("会话存储未初始化");
        }
        
        std::lock_guard<std::mutex> lock(storage_mutex_);
        
        const auto file_path = get_current_account_file_path();
        
        if (account_id.empty()) {
            // 清除当前账户
            if (std::filesystem::exists(file_path)) {
                std::filesystem::remove(file_path);
            }
        } else {
            // 设置当前账户
            write_file(file_path, account_id);
        }
    });
}

std::future<std::optional<std::string>> SessionStore::get_current_account() {
    return std::async(std::launch::async, [this]() -> std::optional<std::string> {
        if (!initialized_.load()) {
            return std::nullopt;
        }
        
        std::lock_guard<std::mutex> lock(storage_mutex_);
        
        const auto file_path = get_current_account_file_path();
        
        if (!std::filesystem::exists(file_path)) {
            return std::nullopt;
        }
        
        try {
            const std::string account_id = read_file(file_path);
            return account_id.empty() ? std::nullopt : std::make_optional(account_id);
        } catch (const std::exception&) {
            return std::nullopt;
        }
    });
}

std::future<std::size_t> SessionStore::cleanup_expired_sessions() {
    return std::async(std::launch::async, [this]() -> std::size_t {
        if (!initialized_.load()) {
            return 0;
        }
        
        std::size_t cleaned_count = 0;
        std::lock_guard<std::mutex> lock(storage_mutex_);
        
        const auto now = std::chrono::system_clock::now();
        const auto ttl_threshold = now - config_.session_ttl;
        
        for (const auto& entry : std::filesystem::directory_iterator(storage_path_)) {
            if (entry.is_regular_file() && entry.path().extension() == ".json") {
                try {
                    const std::string account_id = entry.path().stem().string();
                    
                    auto session_future = get_session(account_id);
                    auto session_opt = session_future.get();
                    
                    if (session_opt) {
                        const auto& session = *session_opt;
                        
                        // 检查是否过期
                        bool should_remove = false;
                        
                        if (session.is_expired()) {
                            should_remove = true;
                        } else if (session.last_used < ttl_threshold) {
                            should_remove = true;
                        }
                        
                        if (should_remove) {
                            secure_delete_file(entry.path());
                            ++cleaned_count;
                        }
                    }
                } catch (const std::exception&) {
                    // 删除损坏的文件
                    std::filesystem::remove(entry.path());
                    ++cleaned_count;
                }
            }
        }
        
        return cleaned_count;
    });
}

// 私有方法实现
void SessionStore::ensure_storage_directory() {
    if (!std::filesystem::exists(storage_path_)) {
        std::filesystem::create_directories(storage_path_);
        std::filesystem::permissions(storage_path_, config_.filesystem.dir_permissions);
    }
}

std::filesystem::path SessionStore::get_session_file_path(const std::string& account_id) const {
    return storage_path_ / (account_id + ".json");
}

std::filesystem::path SessionStore::get_current_account_file_path() const {
    return storage_path_ / "current_account.txt";
}

std::string SessionStore::encrypt_data(const std::string& data) const {
    // 简化的加密实现（实际应用中应使用更强的加密）
    return data; // TODO: 实现真正的加密
}

std::string SessionStore::decrypt_data(const std::string& encrypted_data) const {
    // 简化的解密实现
    return encrypted_data; // TODO: 实现真正的解密
}

std::string SessionStore::read_file(const std::filesystem::path& file_path) const {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        throw SessionStoreException("无法打开文件: " + file_path.string());
    }
    
    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    return content;
}

void SessionStore::write_file(const std::filesystem::path& file_path, const std::string& content) const {
    std::ofstream file(file_path);
    if (!file.is_open()) {
        throw SessionStoreException("无法创建文件: " + file_path.string());
    }
    
    file << content;
    
    // 设置文件权限
    std::filesystem::permissions(file_path, config_.filesystem.file_permissions);
}

void SessionStore::secure_delete_file(const std::filesystem::path& file_path) const {
    // 简化的安全删除（实际应用中应覆写文件内容）
    std::filesystem::remove(file_path);
}

void SessionStore::start_auto_cleanup() {
    cleanup_running_.store(true);
    cleanup_thread_ = std::make_unique<std::thread>([this]() {
        while (cleanup_running_.load()) {
            std::this_thread::sleep_for(config_.cleanup_interval);
            
            if (cleanup_running_.load()) {
                try {
                    auto cleanup_future = cleanup_expired_sessions();
                    cleanup_future.get();
                } catch (const std::exception&) {
                    // 忽略清理错误
                }
            }
        }
    });
}

void SessionStore::stop_auto_cleanup() {
    cleanup_running_.store(false);
    if (cleanup_thread_ && cleanup_thread_->joinable()) {
        cleanup_thread_->join();
    }
}

std::string SessionStore::generate_encryption_key() const {
    // 生成简单的加密密钥
    return "default-encryption-key"; // TODO: 生成真正的随机密钥
}

bool SessionStore::is_valid_account_id(const std::string& account_id) {
    if (account_id.empty() || account_id.length() > 256) {
        return false;
    }
    
    // 简单的字符验证
    for (char c : account_id) {
        if (!std::isalnum(c) && c != '@' && c != '.' && c != '-' && c != '_') {
            return false;
        }
    }
    
    return true;
}
