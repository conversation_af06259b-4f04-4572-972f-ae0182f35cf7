^C:\USERS\<USER>\DESKTOP\AUGMENT.VSCODE-AUGMENT-0.522.0\CPP\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/augment.vscode-augment-0.522.0/cpp -BC:/Users/<USER>/Desktop/augment.vscode-augment-0.522.0/cpp/build --check-stamp-file C:/Users/<USER>/Desktop/augment.vscode-augment-0.522.0/cpp/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
