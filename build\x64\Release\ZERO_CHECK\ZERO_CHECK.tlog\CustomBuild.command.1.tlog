^C:\USERS\<USER>\DESKTOP\AUGMENT.VSCODE-AUGMENT-0.522.0\CPP\BUILD\CMAKEFILES\D9B353690F93CFE4CB21C9D94DDE3DF2\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/augment.vscode-augment-0.522.0/cpp -BC:/Users/<USER>/Desktop/augment.vscode-augment-0.522.0/cpp/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/augment.vscode-augment-0.522.0/cpp/build/AugmentAuthWebServer.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
