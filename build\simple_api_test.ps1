# 简单API测试脚本
# 测试Augment认证服务器的基本功能

$baseUrl = "http://localhost:8080"

Write-Host "🧪 开始API测试..." -ForegroundColor Cyan
Write-Host "目标服务器: $baseUrl" -ForegroundColor White

# 测试1: 获取账户列表
Write-Host "`n[测试1] 获取账户列表" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/accounts" -Method GET -TimeoutSec 10
    Write-Host "✅ 成功 - 响应: $($response | ConvertTo-Json -Compress)" -ForegroundColor Green
} catch {
    Write-Host "❌ 失败 - 错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试2: 获取当前账户
Write-Host "`n[测试2] 获取当前账户" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/current-account" -Method GET -TimeoutSec 10
    Write-Host "✅ 成功 - 响应: $($response | ConvertTo-Json -Compress)" -ForegroundColor Green
} catch {
    Write-Host "❌ 失败 - 错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试3: 开始认证流程
Write-Host "`n[测试3] 开始认证流程" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/start-auth" -Method GET -TimeoutSec 10
    Write-Host "✅ 成功 - 响应包含字段: flowId, authUrl, state" -ForegroundColor Green
    Write-Host "   flowId: $($response.flowId)" -ForegroundColor Gray
    Write-Host "   authUrl: $($response.authUrl)" -ForegroundColor Gray
} catch {
    Write-Host "❌ 失败 - 错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试4: Web界面访问
Write-Host "`n[测试4] Web界面访问" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/" -Method GET -TimeoutSec 10
    if ($response.Content -like "*Augment*" -or $response.Content -like "*认证*") {
        Write-Host "✅ 成功 - Web界面可访问，内容长度: $($response.Content.Length)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  警告 - Web界面可访问但内容异常" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 失败 - 错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试5: 带参数的API
Write-Host "`n[测试5] 带参数API测试" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/complete-auth?callbackUrl=test://callback" -Method GET -TimeoutSec 10
    Write-Host "✅ 成功 - 参数处理正常" -ForegroundColor Green
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "✅ 成功 - 参数验证正常 (400错误预期)" -ForegroundColor Green
    } else {
        Write-Host "❌ 失败 - 错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🎉 API测试完成!" -ForegroundColor Cyan
