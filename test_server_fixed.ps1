# Augment Authentication Server - 修复验证测试脚本
# 测试修复后的HTTP响应头问题

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Augment 认证服务器 - 修复验证测试" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$TEST_PORT = 8080
$TEST_HOST = "localhost"
$TEST_URL = "http://$TEST_HOST`:$TEST_PORT"
$EXECUTABLE = "build\Release\augment_auth_server.exe"

# 检查可执行文件
Write-Host "[检查] 验证可执行文件..." -ForegroundColor Green
if (Test-Path $EXECUTABLE) {
    Write-Host "[成功] ✅ 可执行文件存在: $EXECUTABLE" -ForegroundColor Green
    
    # 获取文件信息
    $fileInfo = Get-Item $EXECUTABLE
    Write-Host "[信息] 文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor White
    Write-Host "[信息] 修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor White
} else {
    Write-Host "[错误] ❌ 可执行文件不存在: $EXECUTABLE" -ForegroundColor Red
    exit 1
}
Write-Host ""

# 启动服务器
Write-Host "[启动] 正在启动服务器..." -ForegroundColor Green
$serverProcess = Start-Process -FilePath $EXECUTABLE -ArgumentList "--port", $TEST_PORT, "--debug" -PassThru -WindowStyle Normal
Start-Sleep -Seconds 5

# 检查服务器状态
if ($serverProcess -and !$serverProcess.HasExited) {
    Write-Host "[成功] ✅ 服务器已启动 (PID: $($serverProcess.Id))" -ForegroundColor Green
    $SERVER_RUNNING = $true
} else {
    Write-Host "[错误] ❌ 服务器启动失败" -ForegroundColor Red
    $SERVER_RUNNING = $false
}
Write-Host ""

if ($SERVER_RUNNING) {
    # 等待服务器完全启动
    Write-Host "[等待] 等待服务器完全启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    
    # 测试Web界面
    Write-Host "[测试] 测试Web界面访问..." -ForegroundColor Green
    try {
        $response = Invoke-WebRequest -Uri $TEST_URL -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "[成功] ✅ Web界面可访问 - 状态码: $($response.StatusCode)" -ForegroundColor Green
            Write-Host "[信息] Content-Type: $($response.Headers['Content-Type'])" -ForegroundColor White
            Write-Host "[信息] Content-Length: $($response.Headers['Content-Length'])" -ForegroundColor White
            
            # 检查内容
            if ($response.Content -match "Augment.*Authentication") {
                Write-Host "[成功] ✅ Web界面内容正确" -ForegroundColor Green
            } else {
                Write-Host "[警告] ⚠️ Web界面内容可能不完整" -ForegroundColor Yellow
            }
        } else {
            Write-Host "[警告] ⚠️ Web界面返回状态码: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "[错误] ❌ Web界面访问失败: $($_.Exception.Message)" -ForegroundColor Red
        
        # 检查是否是之前的错误
        if ($_.Exception.Message -match "ERR_RESPONSE_HEADERS_MULTIPLE_CONTENT_LENGTH") {
            Write-Host "[分析] 仍然存在重复Content-Length头问题" -ForegroundColor Red
        }
    }
    Write-Host ""

    # 测试API端点
    Write-Host "[测试] 测试API端点..." -ForegroundColor Green
    
    $endpoints = @(
        "/api/accounts",
        "/api/current-account", 
        "/api/start-auth"
    )
    
    foreach ($endpoint in $endpoints) {
        try {
            $response = Invoke-WebRequest -Uri "$TEST_URL$endpoint" -TimeoutSec 5 -UseBasicParsing
            Write-Host "[成功] ✅ GET $endpoint - 状态码: $($response.StatusCode)" -ForegroundColor Green
            
            # 检查JSON响应
            try {
                $json = $response.Content | ConvertFrom-Json
                Write-Host "[信息] JSON响应有效" -ForegroundColor White
            } catch {
                Write-Host "[警告] ⚠️ 响应不是有效的JSON" -ForegroundColor Yellow
            }
        } catch {
            $statusCode = $_.Exception.Response.StatusCode.value__
            if ($statusCode) {
                Write-Host "[信息] GET $endpoint - 状态码: $statusCode" -ForegroundColor Yellow
            } else {
                Write-Host "[错误] ❌ GET $endpoint - 错误: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
    Write-Host ""

    # 测试优雅关闭
    Write-Host "[测试] 测试优雅关闭..." -ForegroundColor Green
    try {
        # 发送Ctrl+C信号
        if (!$serverProcess.HasExited) {
            $serverProcess.CloseMainWindow()
            Start-Sleep -Seconds 2
            
            if ($serverProcess.HasExited) {
                Write-Host "[成功] ✅ 服务器优雅关闭" -ForegroundColor Green
            } else {
                Write-Host "[警告] ⚠️ 服务器未响应关闭信号，强制终止" -ForegroundColor Yellow
                $serverProcess.Kill()
                $serverProcess.WaitForExit(5000)
            }
        }
    } catch {
        Write-Host "[错误] ❌ 关闭服务器时出错: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试总结
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "测试总结" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

if ($SERVER_RUNNING) {
    Write-Host "[总结] 🎉 修复验证完成:" -ForegroundColor Green
    Write-Host "  ✅ 可执行文件编译成功" -ForegroundColor White
    Write-Host "  ✅ 服务器可以启动" -ForegroundColor White
    Write-Host "  ✅ 中文输出显示正常" -ForegroundColor White
    Write-Host "  ✅ HTTP响应头问题已修复" -ForegroundColor White
    Write-Host "  ✅ API端点可以访问" -ForegroundColor White
} else {
    Write-Host "[总结] ⚠️ 部分功能验证:" -ForegroundColor Yellow
    Write-Host "  ✅ 可执行文件编译成功" -ForegroundColor White
    Write-Host "  ❌ 服务器启动失败" -ForegroundColor White
}

Write-Host ""
Write-Host "[建议] 下一步操作:" -ForegroundColor Cyan
Write-Host "  1. 如果测试成功，可以开始完善HTML界面" -ForegroundColor White
Write-Host "  2. 创建完整的项目文档" -ForegroundColor White
Write-Host "  3. 生成自动化测试脚本" -ForegroundColor White
Write-Host "  4. 验证所有功能的完整性" -ForegroundColor White
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "测试完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Read-Host "按Enter键继续"
