﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\oauth_pkce_generator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\token_exchanger.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\session_store.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\augment_auth_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\auth_web_server.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\http_client.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\oauth_pkce_generator.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\token_exchanger.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\session_store.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\augment_auth_manager.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\auth_web_server.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\http_client.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\augment.vscode-augment-0.522.0\cpp\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{09746A0B-BC9D-3AD0-BAB0-681735E56894}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{164AF956-ADC2-3FCC-A92B-D927A3C0F0CE}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
