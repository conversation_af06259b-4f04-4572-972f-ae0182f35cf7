@echo off
REM Augment Authentication Server - Functionality Test Script
REM This script verifies all core functionality of the authentication server

echo ========================================
echo Augment Authentication Server Test Suite
echo ========================================
echo.

REM Set variables
set BUILD_DIR=build
set EXECUTABLE=Release\augment_auth_server.exe
set TEST_PORT=8080
set TEST_HOST=localhost
set TEST_URL=http://%TEST_HOST%:%TEST_PORT%

echo [INFO] Starting comprehensive functionality tests...
echo.

REM Test 1: Check if executable exists
echo [TEST 1] Checking executable existence...
if exist "%BUILD_DIR%\%EXECUTABLE%" (
    echo [PASS] ✅ Executable found: %EXECUTABLE%
) else (
    echo [FAIL] ❌ Executable not found: %EXECUTABLE%
    echo [ERROR] Please build the project first using: cmake --build . --config Release
    pause
    exit /b 1
)
echo.

REM Test 2: Help command test
echo [TEST 2] Testing help command...
cd %BUILD_DIR%
%EXECUTABLE% --help > help_output.txt 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [PASS] ✅ Help command executed successfully
    findstr /C:"Usage:" help_output.txt >nul
    if %ERRORLEVEL% EQU 0 (
        echo [PASS] ✅ Help output contains usage information
    ) else (
        echo [WARN] ⚠️ Help output format may be incorrect
    )
) else (
    echo [FAIL] ❌ Help command failed
)
del help_output.txt 2>nul
echo.

REM Test 3: Version/Info test
echo [TEST 3] Testing server information display...
echo [INFO] This test will start the server briefly to check startup messages
start /B %EXECUTABLE% --port %TEST_PORT% --debug > server_output.txt 2>&1
timeout /t 3 /nobreak >nul
taskkill /F /IM augment_auth_server.exe >nul 2>&1

if exist server_output.txt (
    findstr /C:"Starting Augment Authentication Web Server" server_output.txt >nul
    if %ERRORLEVEL% EQU 0 (
        echo [PASS] ✅ Server startup message displayed correctly
    ) else (
        echo [WARN] ⚠️ Server startup message format may be different
    )
    
    findstr /C:"HTTP Server listening" server_output.txt >nul
    if %ERRORLEVEL% EQU 0 (
        echo [PASS] ✅ HTTP server initialization message found
    ) else (
        echo [WARN] ⚠️ HTTP server message not found
    )
) else (
    echo [FAIL] ❌ Could not capture server output
)
del server_output.txt 2>nul
echo.

REM Test 4: Port binding test
echo [TEST 4] Testing port binding capability...
echo [INFO] Starting server on port %TEST_PORT%...
start /B %EXECUTABLE% --port %TEST_PORT% --debug
timeout /t 2 /nobreak >nul

REM Check if port is in use (indicates successful binding)
netstat -an | findstr ":%TEST_PORT%" >nul
if %ERRORLEVEL% EQU 0 (
    echo [PASS] ✅ Server successfully bound to port %TEST_PORT%
    set SERVER_RUNNING=1
) else (
    echo [FAIL] ❌ Server failed to bind to port %TEST_PORT%
    set SERVER_RUNNING=0
)
echo.

REM Test 5: Web interface accessibility test
if %SERVER_RUNNING% EQU 1 (
    echo [TEST 5] Testing web interface accessibility...
    
    REM Try to connect to the web interface
    powershell -Command "try { $response = Invoke-WebRequest -Uri '%TEST_URL%' -TimeoutSec 5; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo [PASS] ✅ Web interface is accessible at %TEST_URL%
        
        REM Test for expected content
        powershell -Command "$response = Invoke-WebRequest -Uri '%TEST_URL%'; if ($response.Content -match 'Augment Authentication Manager') { exit 0 } else { exit 1 }" >nul 2>&1
        if %ERRORLEVEL% EQU 0 (
            echo [PASS] ✅ Web interface contains expected content
        ) else (
            echo [WARN] ⚠️ Web interface content may be different than expected
        )
    ) else (
        echo [FAIL] ❌ Web interface is not accessible
    )
) else (
    echo [SKIP] ⏭️ Skipping web interface test (server not running)
)
echo.

REM Test 6: API endpoint structure test (Updated for GET methods)
if %SERVER_RUNNING% EQU 1 (
    echo [TEST 6] Testing API endpoint availability...

    REM Test main page for API documentation
    powershell -Command "$response = Invoke-WebRequest -Uri '%TEST_URL%'; if ($response.Content -match '/api/') { exit 0 } else { exit 1 }" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo [PASS] ✅ API endpoints are documented on main page
    ) else (
        echo [WARN] ⚠️ API documentation may not be present
    )

    REM Test for specific API endpoints in documentation
    powershell -Command "$response = Invoke-WebRequest -Uri '%TEST_URL%'; if ($response.Content -match '/api/start-auth') { exit 0 } else { exit 1 }" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo [PASS] ✅ start-auth endpoint documented
    )

    powershell -Command "$response = Invoke-WebRequest -Uri '%TEST_URL%'; if ($response.Content -match '/api/current-account') { exit 0 } else { exit 1 }" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo [PASS] ✅ current-account endpoint documented
    )

    REM Test actual GET endpoints (new functionality)
    echo [INFO] Testing GET API endpoints...

    powershell -Command "try { $response = Invoke-WebRequest -Uri '%TEST_URL%/api/accounts' -TimeoutSec 5; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo [PASS] ✅ GET /api/accounts endpoint accessible
    ) else (
        echo [WARN] ⚠️ GET /api/accounts endpoint may not be working
    )

    powershell -Command "try { $response = Invoke-WebRequest -Uri '%TEST_URL%/api/current-account' -TimeoutSec 5; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo [PASS] ✅ GET /api/current-account endpoint accessible
    ) else (
        echo [WARN] ⚠️ GET /api/current-account endpoint may not be working
    )

    powershell -Command "try { $response = Invoke-WebRequest -Uri '%TEST_URL%/api/start-auth' -TimeoutSec 5; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo [PASS] ✅ GET /api/start-auth endpoint accessible
    ) else (
        echo [WARN] ⚠️ GET /api/start-auth endpoint may not be working
    )

) else (
    echo [SKIP] ⏭️ Skipping API endpoint test (server not running)
)
echo.

REM Test 7: Server shutdown test
if %SERVER_RUNNING% EQU 1 (
    echo [TEST 7] Testing server shutdown...
    taskkill /F /IM augment_auth_server.exe >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo [PASS] ✅ Server shutdown successfully
    ) else (
        echo [WARN] ⚠️ Server may have already stopped
    )
) else (
    echo [SKIP] ⏭️ Skipping shutdown test (server not running)
)
echo.

REM Test 8: File structure verification
echo [TEST 8] Verifying project file structure...
set FILES_FOUND=0

if exist "..\main.cpp" (
    echo [PASS] ✅ main.cpp found
    set /a FILES_FOUND+=1
)
if exist "..\auth_web_server.cpp" (
    echo [PASS] ✅ auth_web_server.cpp found
    set /a FILES_FOUND+=1
)
if exist "..\simple_json.hpp" (
    echo [PASS] ✅ simple_json.hpp found
    set /a FILES_FOUND+=1
)
if exist "..\httplib.h" (
    echo [PASS] ✅ httplib.h found
    set /a FILES_FOUND+=1
)
if exist "..\CMakeLists.txt" (
    echo [PASS] ✅ CMakeLists.txt found
    set /a FILES_FOUND+=1
)

if %FILES_FOUND% GEQ 5 (
    echo [PASS] ✅ Core project files are present (%FILES_FOUND%/5)
) else (
    echo [WARN] ⚠️ Some project files may be missing (%FILES_FOUND%/5)
)
echo.

REM Test Summary
echo ========================================
echo TEST SUMMARY
echo ========================================
echo.
echo Project: Augment Authentication Server
echo Test Date: %DATE% %TIME%
echo Test Environment: Windows
echo.

REM Count results (simplified)
echo [INFO] Test execution completed
echo [INFO] Please review the results above
echo.

if %SERVER_RUNNING% EQU 1 (
    echo [SUCCESS] ✅ Core functionality verified:
    echo   - Executable builds and runs
    echo   - Server binds to specified port
    echo   - Web interface is accessible
    echo   - API endpoints are documented
    echo   - GET method endpoints are accessible
    echo   - Modified API structure working
) else (
    echo [PARTIAL] ⚠️ Basic functionality verified:
    echo   - Executable builds successfully
    echo   - Help command works
    echo   - Project structure is correct
    echo   - Server startup attempted
)
echo.

echo [RECOMMENDATION] For full testing:
echo   1. Ensure no other services are using port %TEST_PORT%
echo   2. Check Windows Firewall settings
echo   3. Verify network connectivity
echo   4. Run tests with administrator privileges if needed
echo.

echo ========================================
echo Test script completed
echo ========================================
pause

cd ..
exit /b 0
